#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AdaptiveCrawlerConsolidatedMerged - Phi<PERSON><PERSON> bả<PERSON> hợp nhất tất cả tính năng từ các module.

<PERSON><PERSON><PERSON> bản này kết hợp:
- <PERSON><PERSON> lý form từ adaptive_crawler_form.py
- <PERSON><PERSON> lý JavaScript từ adaptive_crawler_javascript.py
- Xử lý SPA từ adaptive_crawler_spa.py
- Xử lý AJAX từ adaptive_crawler_ajax.py
- Xử lý phân trang từ adaptive_crawler_pagination.py
- Tất cả tính năng từ adaptive_crawler_consolidated.py
"""

import time
import os
import json
import re
import hashlib
import tempfile
import random
import requests
import urllib.robotparser
from typing import Dict, Any, List, Optional, Union, Tuple, Set, Callable
from urllib.parse import urlparse, urljoin, parse_qs, urlunparse, urlencode
from bs4 import BeautifulSoup
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import các module tùy chọn với fallback
try:
    from playwright.sync_api import sync_playwright
    playwright_available = True
except ImportError:
    playwright_available = False
    logger.warning("Playwright not available. Will use requests fallback.")

# Import module tùy chọn cho việc xử lý captcha
try:
    from ..utils.captcha_handler import CaptchaHandler
    captcha_handler_available = True
except ImportError:
    captcha_handler_available = False
    logger.warning("CaptchaHandler not available. Will skip captcha handling.")

# Import module tùy chọn cho việc trích xuất tài liệu
try:
    from ..utils.document_extractor import DocumentExtractor
    document_extractor_available = True
except ImportError:
    document_extractor_available = False
    logger.warning("DocumentExtractor not available. Will use basic content extraction.")

# Import module tùy chọn cho việc xử lý ngôn ngữ
try:
    from ..utils.language_support import LanguageDetector
    language_detector_available = True
except ImportError:
    language_detector_available = False
    logger.warning("LanguageDetector not available. Will use default language detection.")

# Import AdvancedCrawlee modules cho memory optimization
try:
    from deepresearch.src.deep_research_core.agents.advanced_crawlee import ResourceManager, MemoryOptimizedCrawler
    advanced_crawlee_available = True
except ImportError:
    try:
        from ..agents.advanced_crawlee import ResourceManager, MemoryOptimizedCrawler
        advanced_crawlee_available = True
    except ImportError:
        advanced_crawlee_available = False
        logger.warning("AdvancedCrawlee modules not available. Memory optimization will be disabled.")

# Import Error Utils cho error handling
try:
    from ..utils.error_utils import (
        safe_execute, retry, handle_network_errors,
        DeepResearchError, CrawlerError, NetworkError, TimeoutError,
        format_error_message, get_error_details, validate_input, create_error_response
    )
    error_utils_available = True
except ImportError:
    error_utils_available = False
    logger.warning("Error utils not available. Advanced error handling will be disabled.")

    # Define fallback functions
    def safe_execute(func, *args, default_value=None, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception:
            return default_value

    def retry(max_retries=3, delay=1.0, backoff=2.0, exceptions=(Exception,), logger_func=None):
        def decorator(func):
            return func
        return decorator

    def handle_network_errors(func):
        return func

    class DeepResearchError(Exception):
        pass

    class CrawlerError(DeepResearchError):
        pass

    class NetworkError(DeepResearchError):
        pass

    class TimeoutError(DeepResearchError):
        pass

# Import Playwright Handler cho advanced web interaction
try:
    from ..utils.shared.playwright_handler import PlaywrightHandler
    playwright_handler_available = True
except ImportError:
    playwright_handler_available = False
    logger.warning("PlaywrightHandler not available. Advanced web interaction will be disabled.")

    # Define fallback class
    class PlaywrightHandler:
        def __init__(self, **kwargs):
            pass

        def __enter__(self):
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            pass

        def setup(self):
            pass

        def cleanup(self):
            pass

# Import File Processor cho file processing
try:
    from ..utils.shared.file_processor import FileProcessor
    file_processor_available = True
except ImportError:
    file_processor_available = False
    logger.warning("FileProcessor not available. File processing will be disabled.")

    # Define fallback class
    class FileProcessor:
        def __init__(self, **kwargs):
            pass

        def __enter__(self):
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            pass

# Import Content Extraction Utils cho advanced content extraction
try:
    from ..utils.content_extraction_utils import (
        extract_main_content, summarize_content, extract_content_with_selector,
        extract_structured_content, extract_from_complex_sites, detect_site_type,
        get_extractor_for_site_type
    )
    content_extraction_utils_available = True
except ImportError:
    content_extraction_utils_available = False
    logger.warning("Content Extraction Utils not available. Advanced content extraction will be disabled.")

    # Define fallback functions
    def extract_main_content(html, url=None):
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, "html.parser")
        return soup.get_text(" ", strip=True)

    def summarize_content(text, max_length=500):
        return text[:max_length] + "..." if len(text) > max_length else text

    def extract_content_with_selector(html, selector):
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, "html.parser")
        elements = soup.select(selector)
        return " ".join(element.get_text(strip=True) for element in elements)

    def extract_structured_content(html, url=None):
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, "html.parser")
        title = soup.title.text.strip() if soup.title else ""
        return {
            "title": title,
            "text": extract_main_content(html),
            "metadata": {},
            "images": [],
            "tables": [],
            "structured_data": {}
        }

    def extract_from_complex_sites(url, html):
        return {
            "url": url,
            "success": False,
            "error": "Content extraction utils not available",
            "content": extract_main_content(html),
            "text": extract_main_content(html),
            "title": "",
            "metadata": {}
        }

    def detect_site_type(url, html):
        return "general"

    def get_extractor_for_site_type(site_type):
        return None

        def load_file(self, **kwargs):
            return False

        def extract_text(self):
            return ""

        def extract_metadata(self):
            return {}

# Import Content Extraction Utils cho content extraction
try:
    from ..utils.content_extraction_utils import (
        extract_main_content,
        summarize_content,
        extract_content_with_selector,
        extract_structured_content,
        extract_from_complex_sites,
        detect_site_type,
        get_extractor_for_site_type
    )
    content_extraction_utils_available = True
except ImportError:
    content_extraction_utils_available = False
    logger.warning("Content Extraction Utils not available. Content extraction will be limited.")

    # Define fallback functions
    def extract_main_content(html, url=None):
        return ""

    def summarize_content(text, max_length=500):
        return text[:max_length] if text else ""

    def extract_content_with_selector(html, selector):
        return ""

    def extract_structured_content(html, url=None):
        return {"title": "", "text": "", "metadata": {}, "images": [], "tables": [], "structured_data": {}}

    def extract_from_complex_sites(url, html):
        return {"url": url, "success": False, "error": "Content extraction not available"}

    def detect_site_type(url, html):
        return "general"

    def get_extractor_for_site_type(site_type):
        return None

# Import Advanced Monitoring cho performance monitoring
try:
    from ..utils.advanced_monitoring import PerformanceMetrics, SystemMonitor
    advanced_monitoring_available = True
except ImportError:
    advanced_monitoring_available = False
    logger.warning("Advanced Monitoring not available. Performance monitoring will be limited.")

    # Define fallback classes
    class PerformanceMetrics:
        def __init__(self):
            pass

        def record_execution_time(self, operation, duration):
            pass

        def record_memory_usage(self):
            pass

        def record_cpu_usage(self):
            pass

        def record_network_request(self, url, size, success=True):
            pass

        def record_cache_access(self, cache_type, hit):
            pass

        def record_request(self, success=True, error_type=None):
            pass

        def get_summary(self):
            return {"error": "Advanced Monitoring not available"}

    class SystemMonitor:
        def __init__(self, check_interval=60):
            pass

        def start_monitoring(self):
            pass

        def stop_monitoring(self):
            pass

        def get_system_status(self):
            return {"error": "Advanced Monitoring not available"}

# Import User Agent Manager cho user agent management
try:
    from ..utils.shared.user_agent_manager import UserAgentManager, get_random_user_agent
    user_agent_manager_available = True
except ImportError:
    user_agent_manager_available = False
    logger.warning("User Agent Manager not available. User agent management will be limited.")

# Import Site Structure Handler cho site structure analysis
try:
    from ..utils.shared.site_structure_handler import (
        SiteStructureHandler, map_site_structure, analyze_site
    )
    site_structure_handler_available = True
except ImportError:
    site_structure_handler_available = False
    logger.warning("Site Structure Handler not available. Site structure analysis will be limited.")

    # Define fallback classes
    class SiteStructureHandler:
        def __init__(self, **kwargs):
            pass

        def analyze_page(self, url, content=None):
            return {"url": url, "page_type": "unknown", "site_type": "unknown"}

        def extract_navigation(self, url, content=None):
            return []

        def extract_breadcrumbs(self, url, content=None):
            return []

        def extract_pagination(self, url, content=None):
            return {}

        def detect_page_type(self, url, content=None):
            return "unknown"

        def detect_site_type(self, url, content=None):
            return "unknown"

        def build_site_structure(self, start_url, max_depth=None, max_urls=None):
            return {}

        def get_site_structure(self):
            return {}

        def analyze_structure(self):
            return {}

        def find_common_patterns(self):
            return {}

    def map_site_structure(start_url, html_fetcher, **kwargs):
        return {}

    def analyze_site(start_url, html_fetcher, **kwargs):
        return {"structure": {}, "analysis": {}, "patterns": {}}

# Import Language Handler cho language processing
try:
    from ..utils.shared.language_handler import (
        LanguageHandler, detect_language, is_vietnamese_text,
        remove_vietnamese_tones, normalize_vietnamese_text
    )
    language_handler_available = True
except ImportError:
    language_handler_available = False
    logger.warning("Language Handler not available. Language processing will be limited.")

    # Define fallback classes and functions
    class LanguageHandler:
        def __init__(self, **kwargs):
            self.default_lang = kwargs.get('default_lang', 'en')

        def detect_language(self, text):
            return self.default_lang

        def remove_vietnamese_tones(self, text):
            return text

        def normalize_vietnamese_text(self, text):
            return text

        def extract_keywords(self, text, language=None, max_keywords=10):
            return []

        def split_into_sentences(self, text, language=None):
            return [text] if text else []

        def clean_vietnamese_text(self, text):
            return text

        def get_language_name(self, language_code):
            return f"Unknown ({language_code})"

    def detect_language(text):
        return 'en'

    def is_vietnamese_text(text):
        return False

    def remove_vietnamese_tones(text):
        return text

    def normalize_vietnamese_text(text):
        return text

# Import Integration Manager cho module integration
try:
    from ..utils.shared.all_modules_integration import (
        ModuleIntegrationManager, integrate_all_modules
    )
    integration_manager_available = True
except ImportError:
    integration_manager_available = False
    logger.warning("Integration Manager not available. Module integration will be limited.")

    # Define fallback classes
    class ModuleIntegrationManager:
        def __init__(self, agent, **kwargs):
            self.agent = agent
            self.integrated_modules = set()
            self.failed_modules = set()
            self.module_status = {}

        def detect_modules(self):
            return {}

        def integrate_module(self, module_name, module_config=None):
            return False

        def integrate_all_modules(self):
            return {}

        def get_module_status(self):
            return {}

        def get_integrated_modules(self):
            return set()

        def get_failed_modules(self):
            return set()

    def integrate_all_modules(agent, config=None):
        return {}

    class UserAgentManager:
        def __init__(self, **kwargs):
            self.user_agents = {
                "desktop": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"]
            }

        def get_random_user_agent(self, device_type=None):
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

        def get_next_user_agent(self, device_type=None):
            return self.get_random_user_agent(device_type)

        def add_user_agent(self, user_agent, device_type="desktop"):
            pass

        def remove_user_agent(self, user_agent, device_type=None):
            return False

        def get_user_agents_count(self, device_type=None):
            return 1

        def get_all_user_agents(self, device_type=None):
            return self.user_agents if not device_type else self.user_agents.get(device_type, [])

        def set_custom_user_agent(self, user_agent):
            pass

        def enable_rotation(self, interval=10):
            pass

        def disable_rotation(self):
            pass

    def get_random_user_agent(device_type=None):
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"


class AdaptiveCrawlerConsolidatedMerged:
    """
    AdaptiveCrawlerConsolidatedMerged với tất cả tính năng từ các module.
    
    Tính năng:
    - Tìm kiếm đơn URL và nhiều URL
    - Tạo site map
    - Tải xuống media files
    - Xử lý form trên trang web
    - Xử lý JavaScript trên trang web
    - Xử lý SPA (Single Page Application)
    - Xử lý AJAX
    - Xử lý phân trang
    - Xử lý infinite scroll
    - Hỗ trợ Playwright và fallback sang requests
    - Xử lý robots.txt với cache
    - Xoay vòng User-Agent
    """

    def __init__(self, **kwargs):
        """
        Khởi tạo AdaptiveCrawlerConsolidatedMerged.

        Args:
            **kwargs: Các tùy chọn cấu hình bao gồm:
                # Basic settings
                - use_playwright: Sử dụng Playwright để crawl (mặc định: True nếu có sẵn)
                - max_depth: Độ sâu crawl tối đa (mặc định: 2)
                - max_pages: Số trang tối đa để crawl (mặc định: 10)
                - timeout: Thời gian chờ request tính bằng giây (mặc định: 10)

                # User-Agent rotation
                - rotate_user_agents: Bật xoay vòng User-Agent (mặc định: True)
                - user_agents: Danh sách User-Agent tùy chỉnh

                # Robots.txt handling
                - respect_robots: Tôn trọng robots.txt (mặc định: True)
                - robots_cache_ttl: Thời gian cache robots.txt tính bằng giây (mặc định: 3600)

                # Media handling
                - download_media: Tải xuống file media (mặc định: False)
                - download_path: Đường dẫn để lưu file đã tải xuống
                - max_media_size_mb: Kích thước tối đa của file media tính bằng MB (mặc định: 10)
                - extract_file_content: Trích xuất nội dung từ file đã tải xuống (mặc định: False)

                # Site map
                - site_map_enabled: Tạo site map (mặc định: False)

                # JavaScript handling
                - enable_javascript: Bật thực thi JavaScript (mặc định: True)
                - wait_for_selector: Selector cần chờ xuất hiện
                - wait_for_timeout: Thời gian chờ (ms) sau khi tải trang (mặc định: 1000)

                # SPA handling
                - handle_spa: Xử lý SPA (mặc định: False)
                - spa_routes: Các route để điều hướng trong SPA

                # Infinite scroll handling
                - handle_infinite_scroll: Xử lý infinite scroll (mặc định: False)
                - infinite_scroll_max_scrolls: Số lần cuộn tối đa (mặc định: 5)
                - infinite_scroll_timeout: Thời gian chờ (ms) giữa các lần cuộn (mặc định: 1000)

                # AJAX handling
                - handle_ajax: Xử lý request AJAX (mặc định: False)
                - ajax_wait_time: Thời gian chờ (ms) cho các request AJAX hoàn thành (mặc định: 2000)
                - ajax_request_patterns: Danh sách pattern URL để xác định request AJAX

                # Pagination handling
                - handle_pagination: Xử lý phân trang (mặc định: False)
                - pagination_selectors: Các CSS selector để phát hiện phân trang
                - max_pagination_pages: Số trang phân trang tối đa (mặc định: 5)
                
                # Form handling
                - handle_forms: Xử lý form (mặc định: False)
                - form_selectors: Các CSS selector để phát hiện form
                - form_data: Dữ liệu để điền vào form

                # Captcha handling
                - handle_captcha: Xử lý captcha (mặc định: False nếu không có CaptchaHandler)
                - captcha_service: Dịch vụ giải captcha để sử dụng
                - captcha_api_key: API key cho dịch vụ giải captcha
                
                # Language handling
                - detect_language: Phát hiện ngôn ngữ của trang (mặc định: False)
                - target_language: Ngôn ngữ mục tiêu để ưu tiên
        """
        # Basic settings
        self.name = "AdaptiveCrawlerConsolidatedMerged"
        self.version = "1.0.0"
        self.use_playwright = kwargs.get("use_playwright", playwright_available)
        self.max_depth = kwargs.get("max_depth", 2)
        self.max_pages = kwargs.get("max_pages", 10)
        self.timeout = kwargs.get("timeout", 10)

        # User-Agent rotation
        self.rotate_user_agents = kwargs.get("rotate_user_agents", True)
        self.user_agents = kwargs.get("user_agents", [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
        ])

        # Robots.txt handling
        self.respect_robots = kwargs.get("respect_robots", True)
        self.robots_cache_ttl = kwargs.get("robots_cache_ttl", 3600)
        self.robots_cache = {}  # Cache for robots.txt

        # Media handling
        self.download_media = kwargs.get("download_media", False)
        self.download_path = kwargs.get("download_path")
        self.max_media_size_mb = kwargs.get("max_media_size_mb", 10)
        self.extract_file_content = kwargs.get("extract_file_content", False)

        # Site map
        self.site_map_enabled = kwargs.get("site_map_enabled", False)

        # JavaScript handling
        self.enable_javascript = kwargs.get("enable_javascript", True)
        self.wait_for_selector = kwargs.get("wait_for_selector")
        self.wait_for_timeout = kwargs.get("wait_for_timeout", 1000)

        # SPA handling
        self.handle_spa = kwargs.get("handle_spa", False)
        self.spa_routes = kwargs.get("spa_routes", [])

        # Infinite scroll handling
        self.handle_infinite_scroll = kwargs.get("handle_infinite_scroll", False)
        self.infinite_scroll_max_scrolls = kwargs.get("infinite_scroll_max_scrolls", 5)
        self.infinite_scroll_timeout = kwargs.get("infinite_scroll_timeout", 1000)

        # AJAX handling
        self.handle_ajax = kwargs.get("handle_ajax", False)
        self.ajax_wait_time = kwargs.get("ajax_wait_time", 2000)
        self.ajax_request_patterns = kwargs.get("ajax_request_patterns", ["/api/", ".json", "ajax", "xhr"])

        # Pagination handling
        self.handle_pagination = kwargs.get("handle_pagination", False)
        self.pagination_selectors = kwargs.get("pagination_selectors", [
            ".pagination a", "ul.pagination a", ".pager a",
            "nav.pagination a", ".paginate a", ".page-numbers",
            ".pages a", ".page-link", "[class*='pag'] a"
        ])
        self.max_pagination_pages = kwargs.get("max_pagination_pages", 5)

        # Form handling
        self.handle_forms = kwargs.get("handle_forms", False)
        self.form_selectors = kwargs.get("form_selectors", [
            "form", "form[action]", "form[method]"
        ])
        self.form_data = kwargs.get("form_data", {})

        # Captcha handling
        self.handle_captcha = kwargs.get("handle_captcha", captcha_handler_available)
        self.captcha_service = kwargs.get("captcha_service", "2captcha")
        self.captcha_api_key = kwargs.get("captcha_api_key", "")

        # Language handling
        self.detect_language = kwargs.get("detect_language", language_detector_available)
        self.target_language = kwargs.get("target_language", "en")

        # Resource management (from AdvancedCrawlee)
        self.use_memory_optimization = kwargs.get("use_memory_optimization", advanced_crawlee_available)
        self.memory_limit_mb = kwargs.get("memory_limit_mb", 1024)
        self.max_concurrent_processes = kwargs.get("max_concurrent_processes", 4)
        self.cpu_threshold = kwargs.get("cpu_threshold", 0.8)
        self.batch_size = kwargs.get("batch_size", 10)
        self.adaptive_batch_size = kwargs.get("adaptive_batch_size", True)
        self.memory_cleanup_interval = kwargs.get("memory_cleanup_interval", 5)

        # Internal state
        self.crawled_urls = set()
        self.site_map = {}
        self.session = requests.Session()

        # Initialize ResourceManager if available
        self.resource_manager = None
        if self.use_memory_optimization and advanced_crawlee_available:
            try:
                self.resource_manager = ResourceManager(
                    memory_limit_mb=self.memory_limit_mb,
                    max_concurrent_processes=self.max_concurrent_processes,
                    cpu_threshold=self.cpu_threshold
                )
                logger.info(f"ResourceManager initialized with memory limit: {self.memory_limit_mb}MB")
            except Exception as e:
                logger.warning(f"Failed to initialize ResourceManager: {str(e)}")
                self.resource_manager = None

        # Initialize MemoryOptimizedCrawler if available
        self.memory_optimized_crawler = None
        if self.use_memory_optimization and advanced_crawlee_available and self.resource_manager:
            try:
                self.memory_optimized_crawler = MemoryOptimizedCrawler(
                    resource_manager=self.resource_manager,
                    batch_size=self.batch_size,
                    adaptive_batch_size=self.adaptive_batch_size,
                    memory_cleanup_interval=self.memory_cleanup_interval
                )
                logger.info(f"MemoryOptimizedCrawler initialized with batch size: {self.batch_size}")
            except Exception as e:
                logger.warning(f"Failed to initialize MemoryOptimizedCrawler: {str(e)}")
                self.memory_optimized_crawler = None

        # Khởi tạo CaptchaHandler nếu cần
        self.captcha_handler = None
        if self.handle_captcha and captcha_handler_available:
            self.captcha_handler = CaptchaHandler(
                service=self.captcha_service,
                api_key=self.captcha_api_key
            )

        # Khởi tạo DocumentExtractor nếu cần
        self.document_extractor = None
        if document_extractor_available:
            self.document_extractor = DocumentExtractor()

        # Khởi tạo LanguageDetector nếu cần
        self.language_detector = None
        if self.detect_language and language_detector_available:
            self.language_detector = LanguageDetector()

        logger.info(f"Khởi tạo {self.name} v{self.version}")
        logger.info(f"Playwright: {'Có sẵn' if playwright_available else 'Không có sẵn'}")
        logger.info(f"CaptchaHandler: {'Có sẵn' if captcha_handler_available else 'Không có sẵn'}")
        logger.info(f"DocumentExtractor: {'Có sẵn' if document_extractor_available else 'Không có sẵn'}")
        logger.info(f"LanguageDetector: {'Có sẵn' if language_detector_available else 'Không có sẵn'}")
        logger.info(f"AdvancedCrawlee: {'Có sẵn' if advanced_crawlee_available else 'Không có sẵn'}")
        logger.info(f"ResourceManager: {'Có sẵn' if self.resource_manager else 'Không có sẵn'}")
        logger.info(f"MemoryOptimizedCrawler: {'Có sẵn' if self.memory_optimized_crawler else 'Không có sẵn'}")
        logger.info(f"ErrorUtils: {'Có sẵn' if error_utils_available else 'Không có sẵn'}")
        logger.info(f"PlaywrightHandler: {'Có sẵn' if playwright_handler_available else 'Không có sẵn'}")
        logger.info(f"FileProcessor: {'Có sẵn' if file_processor_available else 'Không có sẵn'}")
        logger.info(f"ContentExtractionUtils: {'Có sẵn' if content_extraction_utils_available else 'Không có sẵn'}")
        logger.info(f"AdvancedMonitoring: {'Có sẵn' if advanced_monitoring_available else 'Không có sẵn'}")
        logger.info(f"UserAgentManager: {'Có sẵn' if user_agent_manager_available else 'Không có sẵn'}")
        logger.info(f"SiteStructureHandler: {'Có sẵn' if site_structure_handler_available else 'Không có sẵn'}")
        logger.info(f"LanguageHandler: {'Có sẵn' if language_handler_available else 'Không có sẵn'}")
        logger.info(f"IntegrationManager: {'Có sẵn' if integration_manager_available else 'Không có sẵn'}")

        # Error handling configuration
        self.use_error_handling = kwargs.get("use_error_handling", error_utils_available)
        self.max_retries = kwargs.get("max_retries", 3)
        self.retry_delay = kwargs.get("retry_delay", 1.0)
        self.retry_backoff = kwargs.get("retry_backoff", 2.0)

        # Playwright Handler configuration
        self.use_playwright_handler = kwargs.get("use_playwright_handler", playwright_handler_available)
        self.playwright_headless = kwargs.get("playwright_headless", True)
        self.playwright_browser_type = kwargs.get("playwright_browser_type", "chromium")
        self.playwright_timeout = kwargs.get("playwright_timeout", 30000)
        self.playwright_viewport = kwargs.get("playwright_viewport", {"width": 1280, "height": 720})
        self.playwright_user_agent = kwargs.get("playwright_user_agent", None)
        self.playwright_proxy = kwargs.get("playwright_proxy", None)

        # Initialize PlaywrightHandler if available
        self.playwright_handler = None
        if self.use_playwright_handler and playwright_handler_available:
            try:
                self.playwright_handler = PlaywrightHandler(
                    headless=self.playwright_headless,
                    browser_type=self.playwright_browser_type,
                    timeout=self.playwright_timeout,
                    viewport=self.playwright_viewport,
                    user_agent=self.playwright_user_agent,
                    proxy=self.playwright_proxy,
                    verbose=False,
                    logger=logger
                )
                logger.info("PlaywrightHandler initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize PlaywrightHandler: {str(e)}")
                self.playwright_handler = None

        # File Processor configuration
        self.use_file_processor = kwargs.get("use_file_processor", file_processor_available)
        self.file_processor_encoding = kwargs.get("file_processor_encoding", "utf-8")
        self.file_processor_chunk_size = kwargs.get("file_processor_chunk_size", 4096)
        self.file_processor_extract_metadata = kwargs.get("file_processor_extract_metadata", True)
        self.file_processor_extract_images = kwargs.get("file_processor_extract_images", False)
        self.file_processor_ocr_enabled = kwargs.get("file_processor_ocr_enabled", False)
        self.file_processor_language = kwargs.get("file_processor_language", "eng")
        self.file_processor_max_file_size = kwargs.get("file_processor_max_file_size", 100 * 1024 * 1024)  # 100MB
        self.file_processor_timeout = kwargs.get("file_processor_timeout", 60)

        # Initialize FileProcessor if available
        self.file_processor = None
        if self.use_file_processor and file_processor_available:
            try:
                # FileProcessor will be initialized per file, not globally
                logger.info("FileProcessor configuration initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to configure FileProcessor: {str(e)}")
                self.file_processor = None

        # Content Extraction Utils configuration
        self.use_content_extraction = kwargs.get("use_content_extraction", content_extraction_utils_available)
        self.content_min_length = kwargs.get("content_min_length", 100)
        self.content_max_length = kwargs.get("content_max_length", 50000)
        self.content_remove_noise = kwargs.get("content_remove_noise", True)
        self.content_extract_metadata = kwargs.get("content_extract_metadata", True)
        self.content_extract_links = kwargs.get("content_extract_links", True)
        self.content_extract_images = kwargs.get("content_extract_images", False)
        self.content_extract_tables = kwargs.get("content_extract_tables", False)
        self.content_clean_html = kwargs.get("content_clean_html", True)
        self.content_preserve_structure = kwargs.get("content_preserve_structure", False)
        self.content_extract_structured = kwargs.get("content_extract_structured", False)
        self.content_site_detection = kwargs.get("content_site_detection", True)
        self.content_custom_selectors = kwargs.get("content_custom_selectors", {})
        self.content_blacklist_selectors = kwargs.get("content_blacklist_selectors", [
            'script', 'style', 'nav', 'header', 'footer', '.advertisement', '.ads'
        ])
        self.content_summary_enabled = kwargs.get("content_summary_enabled", False)
        self.content_summary_max_length = kwargs.get("content_summary_max_length", 500)
        self.content_summary_method = kwargs.get("content_summary_method", "extractive")

        # Initialize Content Extraction Utils if available
        self.content_extractor = None
        if self.use_content_extraction and content_extraction_utils_available:
            try:
                logger.info("Content Extraction Utils configuration initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to configure Content Extraction Utils: {str(e)}")
                self.content_extractor = None

        # Advanced Monitoring configuration
        self.use_advanced_monitoring = kwargs.get("use_advanced_monitoring", advanced_monitoring_available)
        self.monitoring_enabled = kwargs.get("monitoring_enabled", True)
        self.system_monitoring_enabled = kwargs.get("system_monitoring_enabled", False)
        self.system_monitoring_interval = kwargs.get("system_monitoring_interval", 60)
        self.performance_tracking_enabled = kwargs.get("performance_tracking_enabled", True)
        self.memory_monitoring_enabled = kwargs.get("memory_monitoring_enabled", True)
        self.cpu_monitoring_enabled = kwargs.get("cpu_monitoring_enabled", True)
        self.network_monitoring_enabled = kwargs.get("network_monitoring_enabled", True)
        self.cache_monitoring_enabled = kwargs.get("cache_monitoring_enabled", True)
        self.error_tracking_enabled = kwargs.get("error_tracking_enabled", True)
        self.execution_time_tracking = kwargs.get("execution_time_tracking", True)
        self.monitoring_auto_start = kwargs.get("monitoring_auto_start", False)
        self.monitoring_save_to_file = kwargs.get("monitoring_save_to_file", False)
        self.monitoring_file_path = kwargs.get("monitoring_file_path", "monitoring_data.json")

        # Initialize Advanced Monitoring if available
        self.performance_metrics = None
        self.system_monitor = None
        if self.use_advanced_monitoring and advanced_monitoring_available:
            try:
                # Initialize PerformanceMetrics
                if self.performance_tracking_enabled:
                    self.performance_metrics = PerformanceMetrics()
                    logger.info("PerformanceMetrics initialized successfully")

                # Initialize SystemMonitor
                if self.system_monitoring_enabled:
                    self.system_monitor = SystemMonitor(check_interval=self.system_monitoring_interval)
                    if self.monitoring_auto_start:
                        self.system_monitor.start_monitoring()
                    logger.info("SystemMonitor initialized successfully")

            except Exception as e:
                logger.warning(f"Failed to initialize Advanced Monitoring: {str(e)}")
                self.performance_metrics = None
                self.system_monitor = None

        # User Agent Manager configuration
        self.use_user_agent_manager = kwargs.get("use_user_agent_manager", user_agent_manager_available)
        self.user_agent_rotation_enabled = kwargs.get("user_agent_rotation_enabled", True)
        self.user_agent_rotation_interval = kwargs.get("user_agent_rotation_interval", 10)
        self.user_agent_device_type = kwargs.get("user_agent_device_type", None)  # desktop, mobile, tablet, bot
        self.user_agent_custom = kwargs.get("user_agent_custom", None)
        self.user_agent_file_path = kwargs.get("user_agent_file_path", None)
        self.user_agent_auto_update = kwargs.get("user_agent_auto_update", False)
        self.user_agent_update_interval_days = kwargs.get("user_agent_update_interval_days", 30)
        self.user_agent_cache_dir = kwargs.get("user_agent_cache_dir", None)

        # Initialize User Agent Manager if available
        self.user_agent_manager = None
        if self.use_user_agent_manager and user_agent_manager_available:
            try:
                # Initialize UserAgentManager
                self.user_agent_manager = UserAgentManager(
                    user_agents_file=self.user_agent_file_path,
                    update_interval_days=self.user_agent_update_interval_days,
                    auto_update=self.user_agent_auto_update,
                    cache_dir=self.user_agent_cache_dir,
                    rotation_enabled=self.user_agent_rotation_enabled,
                    rotation_interval=self.user_agent_rotation_interval,
                    custom_user_agent=self.user_agent_custom
                )

                # Set custom user agent if provided
                if self.user_agent_custom:
                    self.user_agent_manager.set_custom_user_agent(self.user_agent_custom)

                # Enable rotation if configured
                if self.user_agent_rotation_enabled:
                    self.user_agent_manager.enable_rotation(self.user_agent_rotation_interval)

                logger.info("UserAgentManager initialized successfully")

            except Exception as e:
                logger.warning(f"Failed to initialize User Agent Manager: {str(e)}")
                self.user_agent_manager = None

        # Site Structure Handler configuration
        self.use_site_structure_handler = kwargs.get("use_site_structure_handler", site_structure_handler_available)
        self.site_structure_extract_navigation = kwargs.get("site_structure_extract_navigation", True)
        self.site_structure_extract_breadcrumbs = kwargs.get("site_structure_extract_breadcrumbs", True)
        self.site_structure_extract_pagination = kwargs.get("site_structure_extract_pagination", True)
        self.site_structure_extract_forms = kwargs.get("site_structure_extract_forms", False)
        self.site_structure_detect_page_type = kwargs.get("site_structure_detect_page_type", True)
        self.site_structure_detect_site_type = kwargs.get("site_structure_detect_site_type", True)
        self.site_structure_detect_language = kwargs.get("site_structure_detect_language", True)
        self.site_structure_max_concurrent_requests = kwargs.get("site_structure_max_concurrent_requests", 5)
        self.site_structure_cache_enabled = kwargs.get("site_structure_cache_enabled", True)
        self.site_structure_cache_ttl = kwargs.get("site_structure_cache_ttl", 3600 * 24)  # 1 day
        self.site_structure_cache_size = kwargs.get("site_structure_cache_size", 1000)
        self.site_structure_use_playwright = kwargs.get("site_structure_use_playwright", False)
        self.site_structure_respect_robots = kwargs.get("site_structure_respect_robots", True)
        self.site_structure_use_sitemap = kwargs.get("site_structure_use_sitemap", True)
        self.site_structure_max_depth = kwargs.get("site_structure_max_depth", 3)
        self.site_structure_max_urls = kwargs.get("site_structure_max_urls", 1000)
        self.site_structure_max_urls_per_domain = kwargs.get("site_structure_max_urls_per_domain", 100)
        self.site_structure_excluded_extensions = kwargs.get("site_structure_excluded_extensions", None)
        self.site_structure_excluded_patterns = kwargs.get("site_structure_excluded_patterns", None)
        self.site_structure_included_patterns = kwargs.get("site_structure_included_patterns", None)
        self.site_structure_prioritize_patterns = kwargs.get("site_structure_prioritize_patterns", None)

        # Initialize Site Structure Handler if available
        self.site_structure_handler = None
        if self.use_site_structure_handler and site_structure_handler_available:
            try:
                self.site_structure_handler = SiteStructureHandler(
                    respect_robots=self.site_structure_respect_robots,
                    use_sitemap=self.site_structure_use_sitemap,
                    max_depth=self.site_structure_max_depth,
                    max_urls=self.site_structure_max_urls,
                    max_urls_per_domain=self.site_structure_max_urls_per_domain,
                    timeout=self.timeout,
                    max_retries=self.max_retries,
                    retry_delay=self.retry_delay,
                    user_agent=None,  # Will use our user agent manager
                    user_agent_manager=self.user_agent_manager,
                    excluded_extensions=self.site_structure_excluded_extensions,
                    excluded_patterns=self.site_structure_excluded_patterns,
                    included_patterns=self.site_structure_included_patterns,
                    prioritize_patterns=self.site_structure_prioritize_patterns,
                    extract_metadata=True,
                    extract_links=True,
                    extract_images=self.content_extract_images,
                    extract_files=True,
                    extract_structured_data=True,
                    extract_breadcrumbs=self.site_structure_extract_breadcrumbs,
                    extract_navigation=self.site_structure_extract_navigation,
                    extract_pagination=self.site_structure_extract_pagination,
                    extract_forms=self.site_structure_extract_forms,
                    detect_page_type=self.site_structure_detect_page_type,
                    detect_site_type=self.site_structure_detect_site_type,
                    detect_language=self.site_structure_detect_language,
                    max_concurrent_requests=self.site_structure_max_concurrent_requests,
                    cache_enabled=self.site_structure_cache_enabled,
                    cache_ttl=self.site_structure_cache_ttl,
                    cache_size=self.site_structure_cache_size,
                    use_playwright=self.site_structure_use_playwright,
                    playwright_handler=self.playwright_handler,
                    verbose=False
                )
                logger.info("SiteStructureHandler initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Site Structure Handler: {str(e)}")
                self.site_structure_handler = None

        # Language Handler configuration
        self.use_language_handler = kwargs.get("use_language_handler", language_handler_available)
        self.language_default_lang = kwargs.get("language_default_lang", "en")
        self.language_detection_method = kwargs.get("language_detection_method", "auto")
        self.language_fasttext_model_path = kwargs.get("language_fasttext_model_path", None)
        self.language_min_text_length = kwargs.get("language_min_text_length", 20)
        self.language_verbose = kwargs.get("language_verbose", False)
        self.language_auto_detect_enabled = kwargs.get("language_auto_detect_enabled", True)
        self.language_vietnamese_processing = kwargs.get("language_vietnamese_processing", True)
        self.language_remove_tones = kwargs.get("language_remove_tones", False)
        self.language_normalize_text = kwargs.get("language_normalize_text", True)
        self.language_extract_keywords = kwargs.get("language_extract_keywords", False)
        self.language_max_keywords = kwargs.get("language_max_keywords", 10)
        self.language_split_sentences = kwargs.get("language_split_sentences", False)
        self.language_clean_vietnamese = kwargs.get("language_clean_vietnamese", True)
        self.language_target_languages = kwargs.get("language_target_languages", ["vi", "en"])
        self.language_filter_by_language = kwargs.get("language_filter_by_language", False)

        # Initialize Language Handler if available
        self.language_handler = None
        if self.use_language_handler and language_handler_available:
            try:
                self.language_handler = LanguageHandler(
                    default_lang=self.language_default_lang,
                    detection_method=self.language_detection_method,
                    fasttext_model_path=self.language_fasttext_model_path,
                    min_text_length=self.language_min_text_length,
                    verbose=self.language_verbose
                )
                logger.info("LanguageHandler initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Language Handler: {str(e)}")
                self.language_handler = None

        # Integration Manager configuration
        self.use_integration_manager = kwargs.get("use_integration_manager", integration_manager_available)
        self.integration_auto_integrate = kwargs.get("integration_auto_integrate", True)
        self.integration_required_modules = kwargs.get("integration_required_modules", [])
        self.integration_optional_modules = kwargs.get("integration_optional_modules", [])
        self.integration_fallback_enabled = kwargs.get("integration_fallback_enabled", True)
        self.integration_verbose = kwargs.get("integration_verbose", False)
        self.integration_config = kwargs.get("integration_config", {})

        # Initialize Integration Manager if available
        self.integration_manager = None
        self.all_modules_integrated = False
        if self.use_integration_manager and integration_manager_available:
            try:
                self.integration_manager = ModuleIntegrationManager(
                    agent=self,
                    config=self.integration_config,
                    required_modules=self.integration_required_modules,
                    optional_modules=self.integration_optional_modules,
                    fallback_enabled=self.integration_fallback_enabled,
                    verbose=self.integration_verbose
                )
                logger.info("ModuleIntegrationManager initialized successfully")

                # Auto-integrate modules if enabled
                if self.integration_auto_integrate:
                    try:
                        integration_results = self.integration_manager.integrate_all_modules()
                        self.all_modules_integrated = True
                        logger.info(f"Auto-integration completed: {integration_results}")
                    except Exception as e:
                        logger.warning(f"Auto-integration failed: {str(e)}")

            except Exception as e:
                logger.warning(f"Failed to initialize Integration Manager: {str(e)}")
                self.integration_manager = None

    def _get_user_agent(self) -> str:
        """Lấy chuỗi User-Agent (có xoay vòng nếu được bật)."""
        # Use User Agent Manager if available
        if self.use_user_agent_manager and self.user_agent_manager:
            try:
                if self.user_agent_rotation_enabled:
                    return self.user_agent_manager.get_next_user_agent(self.user_agent_device_type)
                else:
                    return self.user_agent_manager.get_random_user_agent(self.user_agent_device_type)
            except Exception as e:
                logger.warning(f"Failed to get user agent from manager: {str(e)}")

        # Fallback to original logic
        if self.rotate_user_agents and self.user_agents:
            return random.choice(self.user_agents)
        return self.user_agents[0] if self.user_agents else f"{self.name}/{self.version}"

    def _can_fetch(self, url: str, user_agent: str = "*") -> bool:
        """
        Kiểm tra xem URL có thể được tải theo robots.txt hay không.

        Args:
            url: URL cần kiểm tra
            user_agent: Chuỗi User-Agent

        Returns:
            bool: True nếu URL có thể được tải
        """
        if not self.respect_robots:
            return True

        try:
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            robots_url = urljoin(base_url, "/robots.txt")

            # Kiểm tra cache trước
            cache_key = base_url
            if cache_key in self.robots_cache:
                cached_data = self.robots_cache[cache_key]
                if time.time() - cached_data["timestamp"] < self.robots_cache_ttl:
                    rp = cached_data["robots_parser"]
                    return rp.can_fetch(user_agent, url)

            # Tải và phân tích robots.txt
            rp = urllib.robotparser.RobotFileParser()
            rp.set_url(robots_url)
            rp.read()

            # Cache kết quả
            self.robots_cache[cache_key] = {
                "robots_parser": rp,
                "timestamp": time.time()
            }

            return rp.can_fetch(user_agent, url)

        except Exception as e:
            logger.warning(f"Lỗi khi kiểm tra robots.txt cho {url}: {str(e)}")
            return True  # Cho phép crawl nếu kiểm tra robots.txt thất bại

    def _crawl_single_page(self, url: str, use_playwright: bool = None) -> Dict[str, Any]:
        """
        Crawl một trang duy nhất và trả về kết quả.

        Args:
            url: URL để crawl
            use_playwright: Có sử dụng Playwright hay không

        Returns:
            Dict chứa kết quả crawl
        """
        if use_playwright is None:
            use_playwright = self.use_playwright

        result = {
            "url": url,
            "content": {},
            "html": None,
            "links": [],
            "media_files": [],
            "success": False,
            "error": None,
            "timestamp": time.time()
        }

        # Kiểm tra robots.txt
        user_agent = self._get_user_agent()
        if not self._can_fetch(url, user_agent):
            result["error"] = "Bị chặn bởi robots.txt"
            return result

        try:
            if use_playwright and playwright_available:
                result = self._crawl_with_playwright(url, user_agent)
            else:
                result = self._crawl_with_requests(url, user_agent)

            # Trích xuất links và media files
            if result.get("success") and result.get("html"):
                soup = BeautifulSoup(result["html"], "html.parser")
                result["links"] = self._extract_links(soup, url)
                result["media_files"] = self._extract_media_files(soup, url)

                # Phát hiện ngôn ngữ nếu được bật
                if self.detect_language and self.language_detector:
                    try:
                        language = self.language_detector.detect(result["html"])
                        result["language"] = language
                    except Exception as e:
                        logger.warning(f"Lỗi khi phát hiện ngôn ngữ: {str(e)}")

                # Xử lý ngôn ngữ với Language Handler nếu được bật
                if self.use_language_handler and self.language_handler:
                    try:
                        # Trích xuất text từ HTML để xử lý ngôn ngữ
                        text_content = soup.get_text(strip=True)
                        if text_content and len(text_content) >= self.language_min_text_length:
                            # Xử lý văn bản với Language Handler
                            language_result = self.process_text_with_language_handler(text_content)

                            # Cập nhật kết quả với thông tin ngôn ngữ
                            result["language_processing"] = language_result

                            # Cập nhật language field nếu chưa có
                            if "language" not in result and language_result.get("language"):
                                result["language"] = language_result["language"]

                            # Thêm thông tin ngôn ngữ vào content
                            if "content" not in result:
                                result["content"] = {}
                            result["content"]["language_info"] = {
                                "detected_language": language_result.get("language"),
                                "language_name": language_result.get("language_name"),
                                "is_vietnamese": language_result.get("language") == "vi",
                                "operations_performed": language_result.get("operations_performed", []),
                                "keywords": language_result.get("metadata", {}).get("keywords", []),
                                "sentence_count": language_result.get("metadata", {}).get("sentence_count", 0)
                            }

                            # Lọc theo ngôn ngữ nếu được bật
                            if language_result.get("filtered_out"):
                                result["language_filtered"] = True
                                result["filter_reason"] = language_result.get("filter_reason")
                                logger.info(f"Page filtered by language: {language_result.get('filter_reason')}")

                    except Exception as e:
                        logger.warning(f"Lỗi khi xử lý ngôn ngữ với Language Handler: {str(e)}")

                # Cập nhật site map nếu được bật
                if self.site_map_enabled:
                    self._update_site_map(url, result)

                # Xử lý captcha nếu được phát hiện và enabled
                if self.handle_captcha and self.captcha_handler and self._is_captcha_page(soup):
                    captcha_result = self._solve_captcha(url, soup)
                    if captcha_result.get("success"):
                        # Crawl lại sau khi giải captcha
                        return self._crawl_single_page(url, use_playwright)
                    else:
                        result["captcha_detected"] = True
                        result["captcha_solved"] = False
                        result["error"] = captcha_result.get("error", "Không thể giải captcha")

            return result

        except Exception as e:
            result["error"] = f"Lỗi khi crawl {url}: {str(e)}"
            return result 

    def _crawl_with_playwright(self, url: str, user_agent: str) -> Dict[str, Any]:
        """Crawl sử dụng Playwright."""
        result = {
            "url": url,
            "content": {},
            "html": None,
            "success": False,
            "error": None,
            "timestamp": time.time()
        }

        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page(user_agent=user_agent)

                try:
                    page.set_default_timeout(self.timeout * 1000)
                    page.goto(url)

                    # Chờ selector nếu được chỉ định
                    if self.wait_for_selector:
                        try:
                            page.wait_for_selector(self.wait_for_selector, timeout=10000)
                        except Exception as e:
                            logger.warning(f"Không thể tìm thấy selector '{self.wait_for_selector}': {str(e)}")

                    # Xử lý infinite scroll nếu được bật
                    if self.handle_infinite_scroll:
                        for i in range(self.infinite_scroll_max_scrolls):
                            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                            page.wait_for_timeout(self.infinite_scroll_timeout)

                    # Chờ AJAX hoàn thành nếu được bật
                    if self.handle_ajax:
                        page.wait_for_timeout(self.ajax_wait_time)

                    # Xử lý SPA nếu được bật
                    if self.handle_spa and self.spa_routes:
                        for route in self.spa_routes:
                            try:
                                full_route = url.rstrip('/') + '/' + route.lstrip('/')
                                page.goto(full_route)
                                page.wait_for_timeout(self.wait_for_timeout)
                            except Exception as e:
                                logger.warning(f"Lỗi khi chuyển đến route '{route}': {str(e)}")

                    # Xử lý form nếu được bật
                    if self.handle_forms and self.form_data:
                        for selector in self.form_selectors:
                            if page.query_selector(selector):
                                try:
                                    # Điền form
                                    for field, value in self.form_data.items():
                                        try:
                                            page.fill(f"input[name='{field}'], textarea[name='{field}']", value)
                                        except Exception as e:
                                            logger.warning(f"Không thể điền '{field}': {str(e)}")
                                    
                                    # Submit form
                                    page.click(f"{selector} button[type='submit'], {selector} input[type='submit']")
                                    page.wait_for_timeout(self.wait_for_timeout)
                                except Exception as e:
                                    logger.warning(f"Lỗi khi xử lý form với selector '{selector}': {str(e)}")

                    # Chờ thêm thời gian nếu cần
                    page.wait_for_timeout(self.wait_for_timeout)

                    # Lấy nội dung HTML
                    html_content = page.content()
                    result["html"] = html_content

                    # Trích xuất metadata và nội dung
                    result["content"] = {
                        "title": page.title(),
                        "url": page.url,
                        "meta": {}
                    }

                    # Trích xuất meta tags
                    for meta in page.query_selector_all("meta"):
                        try:
                            name = meta.get_attribute("name") or meta.get_attribute("property")
                            content = meta.get_attribute("content")
                            if name and content:
                                result["content"]["meta"][name] = content
                        except Exception:
                            pass

                    result["success"] = True

                except Exception as e:
                    result["error"] = f"Lỗi Playwright: {str(e)}"

                finally:
                    browser.close()

        except Exception as e:
            result["error"] = f"Lỗi khi khởi tạo Playwright: {str(e)}"

        return result

    def _crawl_with_requests(self, url: str, user_agent: str) -> Dict[str, Any]:
        """Crawl sử dụng requests."""
        result = {
            "url": url,
            "content": {},
            "html": None,
            "success": False,
            "error": None,
            "timestamp": time.time()
        }

        try:
            headers = {"User-Agent": user_agent}
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()

            html_content = response.text
            result["html"] = html_content

            # Phân tích HTML
            soup = BeautifulSoup(html_content, "html.parser")

            # Trích xuất metadata và nội dung
            result["content"] = {
                "title": soup.title.string if soup.title else "",
                "url": url,
                "meta": {}
            }

            # Trích xuất meta tags
            for meta in soup.find_all("meta"):
                name = meta.get("name") or meta.get("property")
                content = meta.get("content")
                if name and content:
                    result["content"]["meta"][name] = content

            result["success"] = True

        except Exception as e:
            result["error"] = f"Lỗi requests: {str(e)}"

        return result

    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """
        Trích xuất các liên kết từ trang.

        Args:
            soup: Đối tượng BeautifulSoup
            base_url: URL cơ sở

        Returns:
            List[str]: Danh sách URL
        """
        links = []
        parsed_base = urlparse(base_url)
        base_domain = f"{parsed_base.scheme}://{parsed_base.netloc}"

        for a_tag in soup.find_all("a", href=True):
            href = a_tag.get("href", "").strip()
            if not href or href.startswith(("#", "javascript:", "mailto:", "tel:")):
                continue

            # Xử lý URL tương đối
            if href.startswith("/"):
                href = urljoin(base_domain, href)
            elif not href.startswith(("http://", "https://")):
                href = urljoin(base_url, href)

            # Chỉ thêm URL mới
            if href not in links:
                links.append(href)

        return links

    def _extract_media_files(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """
        Trích xuất URLs của các file media từ trang.

        Args:
            soup: Đối tượng BeautifulSoup
            base_url: URL cơ sở

        Returns:
            List[Dict[str, str]]: Danh sách thông tin file media
        """
        media_files = []
        parsed_base = urlparse(base_url)
        base_domain = f"{parsed_base.scheme}://{parsed_base.netloc}"

        # Tìm hình ảnh
        for img_tag in soup.find_all("img", src=True):
            src = img_tag.get("src", "").strip()
            if not src or src.startswith("data:"):
                continue

            # Xử lý URL tương đối
            if src.startswith("/"):
                src = urljoin(base_domain, src)
            elif not src.startswith(("http://", "https://")):
                src = urljoin(base_url, src)

            alt_text = img_tag.get("alt", "")
            media_files.append({
                "url": src,
                "type": "image",
                "alt": alt_text
            })

        # Tìm video
        for video_tag in soup.find_all("video"):
            src = video_tag.get("src", "").strip()
            if src:
                if src.startswith("/"):
                    src = urljoin(base_domain, src)
                elif not src.startswith(("http://", "https://")):
                    src = urljoin(base_url, src)

                media_files.append({
                    "url": src,
                    "type": "video"
                })

            # Kiểm tra source tags
            for source_tag in video_tag.find_all("source", src=True):
                src = source_tag.get("src", "").strip()
                if src:
                    if src.startswith("/"):
                        src = urljoin(base_domain, src)
                    elif not src.startswith(("http://", "https://")):
                        src = urljoin(base_url, src)

                    media_files.append({
                        "url": src,
                        "type": "video"
                    })

        # Tìm audio
        for audio_tag in soup.find_all("audio"):
            src = audio_tag.get("src", "").strip()
            if src:
                if src.startswith("/"):
                    src = urljoin(base_domain, src)
                elif not src.startswith(("http://", "https://")):
                    src = urljoin(base_url, src)

                media_files.append({
                    "url": src,
                    "type": "audio"
                })

            # Kiểm tra source tags
            for source_tag in audio_tag.find_all("source", src=True):
                src = source_tag.get("src", "").strip()
                if src:
                    if src.startswith("/"):
                        src = urljoin(base_domain, src)
                    elif not src.startswith(("http://", "https://")):
                        src = urljoin(base_url, src)

                    media_files.append({
                        "url": src,
                        "type": "audio"
                    })

        return media_files 

    def _update_site_map(self, url: str, result: Dict[str, Any]):
        """Cập nhật site map với kết quả mới."""
        if self.site_map_enabled:
            self.site_map[url] = {
                "title": result.get("content", {}).get("title", ""),
                "links": result.get("links", []),
                "visited": True,
                "timestamp": time.time()
            }

    def _is_captcha_page(self, soup: BeautifulSoup) -> bool:
        """
        Kiểm tra xem trang có chứa captcha hay không.
        
        Args:
            soup: Đối tượng BeautifulSoup
            
        Returns:
            bool: True nếu phát hiện captcha
        """
        # Kiểm tra các dấu hiệu phổ biến của captcha
        captcha_indicators = [
            # reCAPTCHA
            "g-recaptcha",
            "recaptcha",
            # hCaptcha
            "h-captcha",
            "hcaptcha",
            # Các text thông dụng
            "captcha",
            "robot",
            "human verification",
            "xác minh",
            "xác thực",
            "không phải robot"
        ]
        
        # Tìm trong các thẻ có chứa captcha
        for indicator in captcha_indicators:
            if soup.find(lambda tag: indicator.lower() in tag.get_text().lower()):
                return True
            
            if soup.find(lambda tag: tag.has_attr("class") and indicator.lower() in " ".join(tag["class"]).lower()):
                return True
                
            if soup.find(lambda tag: tag.has_attr("id") and indicator.lower() in tag["id"].lower()):
                return True
        
        # Tìm thẻ img có src hoặc alt chứa captcha
        for img in soup.find_all("img"):
            src = img.get("src", "").lower()
            alt = img.get("alt", "").lower()
            if "captcha" in src or "captcha" in alt:
                return True
        
        return False
        
    def _solve_captcha(self, url: str, soup: BeautifulSoup) -> Dict[str, Any]:
        """
        Giải captcha trên trang.
        
        Args:
            url: URL của trang
            soup: Đối tượng BeautifulSoup
            
        Returns:
            Dict[str, Any]: Kết quả giải captcha
        """
        result = {
            "success": False,
            "error": None
        }
        
        if not self.handle_captcha or not self.captcha_handler:
            result["error"] = "Không thể giải captcha vì CaptchaHandler không có sẵn hoặc bị tắt"
            return result
            
        try:
            # Phát hiện loại captcha
            captcha_type = "unknown"
            site_key = None
            
            # Kiểm tra reCAPTCHA
            recaptcha_div = soup.find("div", class_="g-recaptcha")
            if recaptcha_div:
                captcha_type = "recaptcha"
                site_key = recaptcha_div.get("data-sitekey")
                
            # Kiểm tra hCaptcha
            hcaptcha_div = soup.find("div", class_="h-captcha")
            if hcaptcha_div:
                captcha_type = "hcaptcha"
                site_key = hcaptcha_div.get("data-sitekey")
                
            if site_key:
                # Giải captcha
                solution = self.captcha_handler.solve(
                    captcha_type=captcha_type,
                    site_key=site_key,
                    url=url
                )
                
                if solution.get("success"):
                    result["success"] = True
                    result["solution"] = solution.get("solution")
                else:
                    result["error"] = solution.get("error", "Không thể giải captcha")
            else:
                result["error"] = "Không thể xác định site key của captcha"
                
        except Exception as e:
            result["error"] = f"Lỗi khi giải captcha: {str(e)}"
            
        return result

    def crawl(self, url: str, max_depth: int = None, max_pages: int = None) -> Dict[str, Any]:
        """
        Crawl một URL đơn lẻ với độ sâu tùy chọn.

        Args:
            url: URL để crawl
            max_depth: Độ sâu crawl tối đa
            max_pages: Số trang tối đa để crawl

        Returns:
            Dict chứa kết quả crawl
        """
        if max_depth is None:
            max_depth = self.max_depth
            
        if max_pages is None:
            max_pages = self.max_pages

        # Khởi tạo kết quả
        results = {
            "url": url,
            "success": True,
            "pages": [],
            "total_pages": 0,
            "total_links": 0,
            "total_media_files": 0,
            "timestamp": time.time(),
            "statistics": {
                "successful_pages": 0,
                "failed_pages": 0,
                "total_pages": 0,
                "total_links": 0,
                "total_media_files": 0
            }
        }

        # Reset crawled URLs để mỗi lần gọi là một phiên mới
        self.crawled_urls = set()

        try:
            # Crawl trang đầu tiên
            result = self._crawl_single_page(url)
            self.crawled_urls.add(url)
            results["pages"].append(result)
            
            if result["success"]:
                results["statistics"]["successful_pages"] += 1
                results["statistics"]["total_links"] += len(result.get("links", []))
                results["statistics"]["total_media_files"] += len(result.get("media_files", []))
            else:
                results["statistics"]["failed_pages"] += 1
            
            results["statistics"]["total_pages"] += 1
            
            # Nếu được bật pagination thì crawl các trang phân trang
            if self.handle_pagination and result.get("success"):
                try:
                    soup = BeautifulSoup(result["html"], "html.parser")
                    pagination_urls = self._handle_pagination(soup, url)
                    
                    # Crawl trang phân trang
                    for pagination_url in pagination_urls[:self.max_pagination_pages]:
                        if pagination_url not in self.crawled_urls and len(results["pages"]) < max_pages:
                            pagination_result = self._crawl_single_page(pagination_url)
                            self.crawled_urls.add(pagination_url)
                            results["pages"].append(pagination_result)
                            
                            if pagination_result["success"]:
                                results["statistics"]["successful_pages"] += 1
                                results["statistics"]["total_links"] += len(pagination_result.get("links", []))
                                results["statistics"]["total_media_files"] += len(pagination_result.get("media_files", []))
                            else:
                                results["statistics"]["failed_pages"] += 1
                                
                            results["statistics"]["total_pages"] += 1
                except Exception as e:
                    logger.warning(f"Lỗi khi xử lý phân trang: {str(e)}")

            # Crawl đệ quy các URL khác theo độ sâu
            if max_depth > 1 and result.get("success"):
                links = result.get("links", [])
                # Chỉ lấy max_pages - 1 links (đã crawl trang đầu tiên)
                remaining_pages = max_pages - len(results["pages"])
                self._crawl_recursive(links, max_depth - 1, remaining_pages, results)

            # Cập nhật số lượng tổng cộng
            results["total_pages"] = len(results["pages"])
            results["total_links"] = sum(len(page.get("links", [])) for page in results["pages"])
            results["total_media_files"] = sum(len(page.get("media_files", [])) for page in results["pages"])
            
            # Kiểm tra tất cả các trang đã được crawl thành công
            results["success"] = all(page.get("success", False) for page in results["pages"])

        except Exception as e:
            results["success"] = False
            results["error"] = f"Lỗi khi crawl: {str(e)}"

        return results

    def crawl_multiple(self, urls: List[str], max_depth: int = None, max_pages: int = None) -> Dict[str, Any]:
        """
        Crawl nhiều URL với độ sâu tùy chọn.

        Args:
            urls: Danh sách URLs để crawl
            max_depth: Độ sâu crawl tối đa
            max_pages: Số trang tối đa để crawl

        Returns:
            Dict chứa kết quả crawl
        """
        if max_depth is None:
            max_depth = self.max_depth
            
        if max_pages is None:
            max_pages = self.max_pages

        # Khởi tạo kết quả
        results = {
            "urls": urls,
            "success": True,
            "successful_crawls": 0,
            "failed_crawls": 0,
            "results": [],
            "timestamp": time.time()
        }

        # Theo dõi tổng số trang đã crawl
        total_pages_crawled = 0
        pages_per_url = max(1, max_pages // len(urls))  # Chia đều số trang cho mỗi URL

        try:
            for url in urls:
                # Kiểm tra xem đã đạt đến giới hạn trang chưa
                if total_pages_crawled >= max_pages:
                    break

                # Tính toán số trang còn lại cho URL này
                remaining_pages = max_pages - total_pages_crawled
                pages_for_this_url = min(pages_per_url, remaining_pages)

                # Crawl URL
                result = self.crawl(url, max_depth, pages_for_this_url)
                
                # Cập nhật số lượng trang đã crawl
                total_pages_crawled += result.get("total_pages", 0)
                
                # Cập nhật kết quả
                if result.get("success", False):
                    results["successful_crawls"] += 1
                else:
                    results["failed_crawls"] += 1
                    
                results["results"].append(result)

            # Cập nhật thống kê tổng cộng
            results["total_pages"] = sum(r.get("total_pages", 0) for r in results["results"])
            results["total_links"] = sum(r.get("total_links", 0) for r in results["results"])
            results["total_media_files"] = sum(r.get("total_media_files", 0) for r in results["results"])
            
            # Kiểm tra tất cả các URL đã được crawl thành công
            results["success"] = all(r.get("success", False) for r in results["results"])

        except Exception as e:
            results["success"] = False
            results["error"] = f"Lỗi khi crawl nhiều URL: {str(e)}"

        return results

    def _crawl_recursive(self, links: List[str], remaining_depth: int, remaining_pages: int, results: Dict[str, Any]):
        """
        Crawl đệ quy danh sách các URL.

        Args:
            links: Danh sách URLs để crawl
            remaining_depth: Độ sâu còn lại
            remaining_pages: Số trang còn lại
            results: Dict kết quả để cập nhật
        """
        if remaining_depth <= 0 or remaining_pages <= 0 or not links:
            return

        # Ưu tiên các URL chưa được crawl
        for link in links:
            if link in self.crawled_urls or len(results["pages"]) >= self.max_pages:
                continue

            # Crawl URL
            result = self._crawl_single_page(link)
            self.crawled_urls.add(link)
            results["pages"].append(result)
            
            # Cập nhật thống kê
            if result["success"]:
                results["statistics"]["successful_pages"] += 1
                results["statistics"]["total_links"] += len(result.get("links", []))
                results["statistics"]["total_media_files"] += len(result.get("media_files", []))
            else:
                results["statistics"]["failed_pages"] += 1
                
            results["statistics"]["total_pages"] += 1
            
            # Tiếp tục crawl đệ quy nếu cần
            if remaining_depth > 1 and result.get("success"):
                new_links = result.get("links", [])
                new_remaining_pages = remaining_pages - 1
                self._crawl_recursive(new_links, remaining_depth - 1, new_remaining_pages, results)

            # Kiểm tra giới hạn số trang
            if len(results["pages"]) >= self.max_pages:
                break 

    def _handle_pagination(self, soup, url: str) -> List[str]:
        """
        Xử lý phân trang và trả về danh sách các URL trang tiếp theo.

        Args:
            soup: BeautifulSoup object của trang hiện tại
            url: URL của trang hiện tại

        Returns:
            List[str]: Danh sách các URL trang tiếp theo
        """
        pagination_urls = []
        base_url = urlparse(url)
        base_domain = f"{base_url.scheme}://{base_url.netloc}"

        # Sử dụng các selector được cấu hình
        pagination_selectors = self.pagination_selectors

        # Tìm các liên kết phân trang
        for selector in pagination_selectors:
            try:
                pagination_links = soup.select(selector)
                if pagination_links:
                    for link in pagination_links:
                        href = link.get('href')
                        if href and not href.startswith('#') and 'javascript:' not in href:
                            # Xử lý URL tương đối
                            if href.startswith('/'):
                                href = f"{base_domain}{href}"
                            elif not href.startswith(('http://', 'https://')):
                                href = urljoin(url, href)

                            # Chỉ thêm URL mới
                            if href not in pagination_urls and href != url:
                                pagination_urls.append(href)
            except Exception as e:
                logger.warning(f"Lỗi khi xử lý phân trang với selector {selector}: {str(e)}")

        # Tìm các nút "Trang tiếp" hoặc "Trang sau"
        next_page_texts = ['next', 'next page', 'trang tiếp', 'trang sau', 'tiếp theo', '›', '»', 'trang kế']
        for link in soup.find_all('a'):
            try:
                link_text = link.get_text().lower().strip()
                if any(text in link_text for text in next_page_texts):
                    href = link.get('href')
                    if href and not href.startswith('#') and 'javascript:' not in href:
                        # Xử lý URL tương đối
                        if href.startswith('/'):
                            href = f"{base_domain}{href}"
                        elif not href.startswith(('http://', 'https://')):
                            href = urljoin(url, href)

                        # Chỉ thêm URL mới
                        if href not in pagination_urls and href != url:
                            pagination_urls.append(href)
            except Exception:
                continue

        # Tìm các nút có thuộc tính rel="next"
        for link in soup.find_all('a', attrs={'rel': 'next'}):
            try:
                href = link.get('href')
                if href and not href.startswith('#') and 'javascript:' not in href:
                    # Xử lý URL tương đối
                    if href.startswith('/'):
                        href = f"{base_domain}{href}"
                    elif not href.startswith(('http://', 'https://')):
                        href = urljoin(url, href)

                    # Chỉ thêm URL mới
                    if href not in pagination_urls and href != url:
                        pagination_urls.append(href)
            except Exception:
                continue

        # Giới hạn số lượng URL phân trang theo cấu hình
        if len(pagination_urls) > self.max_pagination_pages:
            pagination_urls = pagination_urls[:self.max_pagination_pages]
            logger.info(f"Giới hạn số lượng URL phân trang từ {len(pagination_urls)} xuống {self.max_pagination_pages}")

        return pagination_urls

    def crawl_website(self, url: str, max_depth: int = None, max_pages: int = None) -> Dict[str, Any]:
        """
        Crawl một trang web hoàn chỉnh, bắt đầu từ URL được cung cấp.

        Args:
            url: URL để bắt đầu crawl
            max_depth: Độ sâu crawl tối đa
            max_pages: Số trang tối đa để crawl

        Returns:
            Dict chứa kết quả crawl
        """
        if max_depth is None:
            max_depth = self.max_depth
            
        if max_pages is None:
            max_pages = self.max_pages

        # Khởi tạo kết quả
        results = {
            "url": url,
            "success": True,
            "pages": [],
            "site_map": {},
            "total_pages": 0,
            "total_links": 0,
            "total_media_files": 0,
            "timestamp": time.time(),
            "statistics": {
                "successful_pages": 0,
                "failed_pages": 0,
                "total_pages": 0,
                "internal_links": 0,
                "external_links": 0,
                "total_links": 0,
                "total_media_files": 0
            }
        }

        # Parse tên miền chính
        parsed_url = urlparse(url)
        base_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        
        # Reset crawled URLs để mỗi lần gọi là một phiên mới
        self.crawled_urls = set()
        self.site_map = {}
        
        # Bắt buộc bật site map cho crawl website
        original_site_map_enabled = self.site_map_enabled
        self.site_map_enabled = True

        try:
            # Crawl URL đầu tiên
            result = self._crawl_single_page(url)
            self.crawled_urls.add(url)
            results["pages"].append(result)
            
            if result["success"]:
                results["statistics"]["successful_pages"] += 1
                
                # Phân loại các liên kết thành internal và external
                for link in result.get("links", []):
                    if self._is_same_domain(link, base_domain):
                        results["statistics"]["internal_links"] += 1
                    else:
                        results["statistics"]["external_links"] += 1
                
                results["statistics"]["total_links"] += len(result.get("links", []))
                results["statistics"]["total_media_files"] += len(result.get("media_files", []))
            else:
                results["statistics"]["failed_pages"] += 1
                
            results["statistics"]["total_pages"] += 1
            
            # Tiếp tục chỉ với các liên kết internal
            if result.get("success"):
                internal_links = [link for link in result.get("links", []) if self._is_same_domain(link, base_domain)]
                
                # Crawl theo chiều rộng trước cho trang web
                to_crawl = internal_links.copy()
                crawled = {url}
                
                while to_crawl and len(results["pages"]) < max_pages and len(crawled) < max_pages:
                    current_url = to_crawl.pop(0)
                    
                    if current_url in crawled:
                        continue
                        
                    # Crawl URL
                    result = self._crawl_single_page(current_url)
                    crawled.add(current_url)
                    results["pages"].append(result)
                    
                    # Cập nhật thống kê
                    if result["success"]:
                        results["statistics"]["successful_pages"] += 1
                        
                        # Phân loại các liên kết thành internal và external
                        for link in result.get("links", []):
                            if self._is_same_domain(link, base_domain):
                                results["statistics"]["internal_links"] += 1
                                # Thêm liên kết nội bộ mới vào queue
                                if link not in crawled and link not in to_crawl and len(crawled) + len(to_crawl) < max_pages:
                                    to_crawl.append(link)
                            else:
                                results["statistics"]["external_links"] += 1
                        
                        results["statistics"]["total_links"] += len(result.get("links", []))
                        results["statistics"]["total_media_files"] += len(result.get("media_files", []))
                    else:
                        results["statistics"]["failed_pages"] += 1
                        
                    results["statistics"]["total_pages"] += 1
            
            # Cập nhật số lượng tổng cộng
            results["total_pages"] = len(results["pages"])
            results["total_links"] = results["statistics"]["total_links"]
            results["total_media_files"] = results["statistics"]["total_media_files"]
            results["site_map"] = self.site_map
            
            # Kiểm tra tất cả các trang đã được crawl thành công
            results["success"] = results["statistics"]["successful_pages"] > 0

        except Exception as e:
            results["success"] = False
            results["error"] = f"Lỗi khi crawl website: {str(e)}"
            
        # Restore site map enabled setting
        self.site_map_enabled = original_site_map_enabled

        return results

    def _is_same_domain(self, url: str, base_domain: str) -> bool:
        """
        Kiểm tra xem URL có cùng domain với domain cơ sở không.

        Args:
            url: URL để kiểm tra
            base_domain: Domain cơ sở

        Returns:
            bool: True nếu cùng domain
        """
        try:
            parsed_url = urlparse(url)
            url_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
            return url_domain == base_domain
        except Exception:
            return False

    def get_site_map(self) -> Dict[str, Any]:
        """Trả về site map hiện tại."""
        return self.site_map

    def clear_cache(self):
        """Xóa tất cả cache."""
        self.robots_cache = {}
        logger.info("Đã xóa tất cả cache")
        
    def process_form(self, url: str, form_data: Dict[str, Any], submit: bool = True, form_selector: str = None) -> Dict[str, Any]:
        """
        Xử lý form trên trang web.
        
        Args:
            url: URL của trang web
            form_data: Dữ liệu form
            submit: Có submit form hay không
            form_selector: CSS selector của form
            
        Returns:
            Dict[str, Any]: Kết quả xử lý form
        """
        result = {
            "success": False,
            "url": url,
            "error": None,
            "html_content": None,
            "form_found": False,
            "form_submitted": False
        }
        
        if not self.use_playwright:
            result["error"] = "Cần bật use_playwright để xử lý form"
            return result
            
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page(user_agent=self._get_user_agent())
                
                try:
                    page.set_default_timeout(self.timeout * 1000)
                    page.goto(url)
                    
                    # Tìm form dựa vào selector
                    form_found = False
                    if form_selector:
                        if page.query_selector(form_selector):
                            form_found = True
                    else:
                        # Thử tìm bất kỳ form nào
                        for selector in self.form_selectors:
                            if page.query_selector(selector):
                                form_selector = selector
                                form_found = True
                                break
                                
                    if not form_found:
                        result["error"] = "Không tìm thấy form"
                        return result
                        
                    result["form_found"] = True
                    
                    # Điền vào form
                    for field, value in form_data.items():
                        try:
                            # Thử các selector khác nhau để tìm field
                            selectors = [
                                f"input[name='{field}']", 
                                f"textarea[name='{field}']",
                                f"select[name='{field}']",
                                f"#{field}",
                                f"[aria-label='{field}']",
                                f"[placeholder*='{field}']"
                            ]
                            
                            for selector in selectors:
                                if page.query_selector(selector):
                                    # Nếu là select
                                    if selector.startswith("select"):
                                        page.select_option(selector, value)
                                    # Nếu là checkbox hoặc radio
                                    elif page.query_selector(f"{selector}[type='checkbox'], {selector}[type='radio']"):
                                        if value:
                                            page.check(selector)
                                        else:
                                            page.uncheck(selector)
                                    # Nếu là input/textarea thông thường
                                    else:
                                        page.fill(selector, value)
                                    break
                        except Exception as e:
                            logger.warning(f"Lỗi khi điền vào trường {field}: {str(e)}")
                    
                    # Submit form nếu cần
                    if submit:
                        try:
                            submit_selectors = [
                                f"{form_selector} button[type='submit']",
                                f"{form_selector} input[type='submit']",
                                "button[type='submit']",
                                "input[type='submit']",
                                "button:has-text('Submit')",
                                "button:has-text('Gửi')",
                                "button:has-text('Tìm')",
                                "button:has-text('Search')",
                                "button:has-text('OK')",
                                "button:has-text('Đăng nhập')",
                                "button:has-text('Login')"
                            ]
                            
                            submitted = False
                            for selector in submit_selectors:
                                if page.query_selector(selector):
                                    page.click(selector)
                                    submitted = True
                                    break
                                    
                            if not submitted:
                                # Thử submit form bằng JavaScript
                                page.evaluate(f"document.querySelector('{form_selector}').submit()")
                                submitted = True
                                
                            result["form_submitted"] = submitted
                            
                            # Chờ trang tải lại sau khi submit
                            page.wait_for_load_state("networkidle")
                        except Exception as e:
                            result["error"] = f"Lỗi khi submit form: {str(e)}"
                    
                    # Lấy nội dung HTML sau khi xử lý form
                    html_content = page.content()
                    result["html_content"] = html_content
                    
                    result["success"] = True
                    
                except Exception as e:
                    result["error"] = f"Lỗi khi xử lý form: {str(e)}"
                    
                finally:
                    browser.close()
                    
        except Exception as e:
            result["error"] = f"Lỗi khi khởi tạo Playwright: {str(e)}"
            
        return result

    # ===== RESOURCE MANAGEMENT METHODS (from AdvancedCrawlee) =====

    def get_resource_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái tài nguyên hiện tại.

        Returns:
            Dict[str, Any]: Thông tin tài nguyên
        """
        if not self.resource_manager:
            return {
                "available": False,
                "error": "ResourceManager not available"
            }

        try:
            resource_usage = self.resource_manager.get_resource_usage()
            active_processes = self.resource_manager.get_active_processes()

            return {
                "available": True,
                "resource_usage": resource_usage,
                "active_processes": active_processes,
                "memory_optimization_enabled": self.use_memory_optimization,
                "memory_limit_mb": self.memory_limit_mb,
                "max_concurrent_processes": self.max_concurrent_processes,
                "cpu_threshold": self.cpu_threshold
            }
        except Exception as e:
            return {
                "available": False,
                "error": f"Error getting resource status: {str(e)}"
            }

    def optimize_batch_size(self) -> int:
        """
        Tự động điều chỉnh batch size dựa trên tài nguyên hiện tại.

        Returns:
            int: Batch size được tối ưu
        """
        if not self.resource_manager:
            return self.batch_size

        try:
            resource_usage = self.resource_manager.get_resource_usage()
            memory_percent = resource_usage.get("memory_mb", 0) / self.memory_limit_mb * 100
            cpu_percent = resource_usage.get("cpu_percent", 0)

            # Điều chỉnh batch size dựa trên sử dụng tài nguyên
            if memory_percent > 80 or cpu_percent > 80:
                # Giảm batch size khi tài nguyên cao
                optimized_size = max(1, self.batch_size // 2)
            elif memory_percent < 50 and cpu_percent < 50:
                # Tăng batch size khi tài nguyên thấp
                optimized_size = min(self.batch_size * 2, 20)
            else:
                optimized_size = self.batch_size

            logger.info(f"Optimized batch size: {optimized_size} (memory: {memory_percent:.1f}%, cpu: {cpu_percent:.1f}%)")
            return optimized_size

        except Exception as e:
            logger.warning(f"Error optimizing batch size: {str(e)}")
            return self.batch_size

    def cleanup_memory(self):
        """Dọn dẹp bộ nhớ."""
        try:
            import gc
            gc.collect()
            logger.info("Memory cleanup completed")
        except Exception as e:
            logger.warning(f"Error during memory cleanup: {str(e)}")

    def crawl_urls_optimized(self, urls: List[str], **kwargs) -> Dict[str, Any]:
        """
        Crawl nhiều URL với tối ưu hóa bộ nhớ.

        Args:
            urls: Danh sách URL cần crawl
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        if not self.memory_optimized_crawler:
            # Fallback to regular crawl_multiple
            logger.warning("MemoryOptimizedCrawler not available, using regular crawl_multiple")
            return self.crawl_multiple(urls, **kwargs)

        try:
            max_depth = kwargs.get('max_depth', self.max_depth)
            max_pages = kwargs.get('max_pages', self.max_pages)

            # Sử dụng MemoryOptimizedCrawler
            result = self.memory_optimized_crawler.crawl_urls(
                urls=urls,
                max_depth=max_depth,
                detailed_scraping=True
            )

            # Chuyển đổi format kết quả để tương thích
            if result.get("success", False):
                return {
                    "success": True,
                    "results": result.get("results", []),
                    "total_urls": len(urls),
                    "successful_crawls": result.get("count", 0),
                    "failed_crawls": len(urls) - result.get("count", 0),
                    "failed_urls": result.get("failed_urls", []),
                    "stats": result.get("stats", {}),
                    "total_pages": result.get("count", 0),
                    "total_links": 0,  # Cần tính toán từ results
                    "total_media_files": 0  # Cần tính toán từ results
                }
            else:
                return {
                    "success": False,
                    "error": "Memory optimized crawl failed",
                    "results": [],
                    "failed_urls": urls
                }

        except Exception as e:
            logger.error(f"Error in optimized crawl: {str(e)}")
            # Fallback to regular crawl
            return self.crawl_multiple(urls, **kwargs)

    # ===== ERROR HANDLING METHODS (from Error Utils) =====

    @handle_network_errors
    def crawl_with_error_handling(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Crawl URL với error handling nâng cao.

        Args:
            url: URL cần crawl
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl với error handling
        """
        if not self.use_error_handling:
            return self.crawl(url, **kwargs)

        try:
            # Validate input
            if error_utils_available:
                validate_input({"url": url}, required_fields=["url"], field_types={"url": str})

            # Execute with retry
            @retry(max_retries=self.max_retries, delay=self.retry_delay, backoff=self.retry_backoff)
            def _crawl_with_retry():
                return self.crawl(url, **kwargs)

            result = _crawl_with_retry()

            # Add error handling metadata
            result["error_handling"] = {
                "enabled": True,
                "max_retries": self.max_retries,
                "retry_delay": self.retry_delay,
                "retry_backoff": self.retry_backoff
            }

            return result

        except Exception as e:
            if error_utils_available:
                error_details = get_error_details(e)
                formatted_message = format_error_message(e)

                return create_error_response(e, status_code=500)
            else:
                return {
                    "success": False,
                    "error": f"Error crawling {url}: {str(e)}",
                    "error_handling": {
                        "enabled": False,
                        "reason": "Error utils not available"
                    }
                }

    def safe_crawl_multiple(self, urls: List[str], **kwargs) -> Dict[str, Any]:
        """
        Crawl nhiều URL một cách an toàn với error handling.

        Args:
            urls: Danh sách URL cần crawl
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl an toàn
        """
        if not self.use_error_handling:
            return self.crawl_multiple(urls, **kwargs)

        results = {
            "success": False,
            "results": [],
            "successful_crawls": 0,
            "failed_crawls": 0,
            "failed_urls": [],
            "total_urls": len(urls),
            "error_handling": {
                "enabled": True,
                "errors": []
            }
        }

        for url in urls:
            try:
                # Use safe_execute for each URL
                result = safe_execute(
                    self.crawl,
                    url,
                    default_value={
                        "success": False,
                        "error": f"Failed to crawl {url}",
                        "url": url
                    },
                    **kwargs
                )

                if result.get("success", False):
                    results["successful_crawls"] += 1
                    results["results"].append(result)
                else:
                    results["failed_crawls"] += 1
                    results["failed_urls"].append(url)
                    if error_utils_available:
                        results["error_handling"]["errors"].append({
                            "url": url,
                            "error": result.get("error", "Unknown error")
                        })

            except Exception as e:
                results["failed_crawls"] += 1
                results["failed_urls"].append(url)

                if error_utils_available:
                    error_details = get_error_details(e)
                    results["error_handling"]["errors"].append({
                        "url": url,
                        "error": format_error_message(e),
                        "details": error_details
                    })

        results["success"] = results["successful_crawls"] > 0
        return results

    def validate_crawl_input(self, **kwargs) -> Dict[str, Any]:
        """
        Validate input parameters cho crawling.

        Args:
            **kwargs: Các tham số cần validate

        Returns:
            Dict[str, Any]: Kết quả validation
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        if not error_utils_available:
            validation_result["warnings"].append("Error utils not available, basic validation only")
            return validation_result

        try:
            # Validate URL if provided
            if "url" in kwargs:
                url = kwargs["url"]
                if not isinstance(url, str):
                    validation_result["valid"] = False
                    validation_result["errors"].append("URL must be a string")
                elif not url.startswith(("http://", "https://")):
                    validation_result["valid"] = False
                    validation_result["errors"].append("URL must start with http:// or https://")

            # Validate URLs list if provided
            if "urls" in kwargs:
                urls = kwargs["urls"]
                if not isinstance(urls, list):
                    validation_result["valid"] = False
                    validation_result["errors"].append("URLs must be a list")
                elif not all(isinstance(url, str) for url in urls):
                    validation_result["valid"] = False
                    validation_result["errors"].append("All URLs must be strings")
                elif not all(url.startswith(("http://", "https://")) for url in urls):
                    validation_result["valid"] = False
                    validation_result["errors"].append("All URLs must start with http:// or https://")

            # Validate numeric parameters
            numeric_params = ["max_depth", "max_pages", "timeout"]
            for param in numeric_params:
                if param in kwargs:
                    value = kwargs[param]
                    if not isinstance(value, (int, float)) or value < 0:
                        validation_result["valid"] = False
                        validation_result["errors"].append(f"{param} must be a non-negative number")

            # Validate boolean parameters
            boolean_params = ["use_playwright", "download_media", "handle_captcha"]
            for param in boolean_params:
                if param in kwargs:
                    value = kwargs[param]
                    if not isinstance(value, bool):
                        validation_result["valid"] = False
                        validation_result["errors"].append(f"{param} must be a boolean")

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Validation error: {str(e)}")

        return validation_result

    # ===== PLAYWRIGHT HANDLER METHODS (from PlaywrightHandler) =====

    def crawl_with_playwright_handler(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Crawl URL sử dụng PlaywrightHandler nâng cao.

        Args:
            url: URL cần crawl
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl với PlaywrightHandler
        """
        if not self.use_playwright_handler or not self.playwright_handler:
            # Fallback to regular crawl
            return self.crawl(url, **kwargs)

        result = {
            "url": url,
            "success": False,
            "error": None,
            "content": None,
            "links": [],
            "media_files": [],
            "playwright_handler": {
                "enabled": True,
                "browser_type": self.playwright_browser_type,
                "headless": self.playwright_headless
            }
        }

        try:
            with self.playwright_handler as handler:
                # Setup handler
                handler.setup()

                # Create page
                page = handler.create_page()

                # Navigate to URL
                navigation_result = handler.navigate(page, url)

                if navigation_result.get("success", False):
                    # Extract content
                    content = handler.extract_content(page)
                    result["content"] = content.get("text", "")
                    result["html"] = content.get("html", "")
                    result["title"] = content.get("title", "")
                    result["main_content"] = content.get("main_content", "")

                    # Extract links
                    links = handler.extract_links(page)
                    result["links"] = [link["url"] for link in links if link.get("url")]
                    result["link_details"] = links

                    # Extract media files (basic implementation)
                    try:
                        media_files = page.evaluate("""() => {
                            const media = [];

                            // Images
                            document.querySelectorAll('img[src]').forEach(img => {
                                media.push({
                                    url: img.src,
                                    type: 'image',
                                    alt: img.alt || null
                                });
                            });

                            // Videos
                            document.querySelectorAll('video[src], video source[src]').forEach(video => {
                                const src = video.src || video.querySelector('source')?.src;
                                if (src) {
                                    media.push({
                                        url: src,
                                        type: 'video'
                                    });
                                }
                            });

                            // Audio
                            document.querySelectorAll('audio[src], audio source[src]').forEach(audio => {
                                const src = audio.src || audio.querySelector('source')?.src;
                                if (src) {
                                    media.push({
                                        url: src,
                                        type: 'audio'
                                    });
                                }
                            });

                            return media;
                        }""")

                        result["media_files"] = media_files
                    except Exception as e:
                        logger.warning(f"Error extracting media files: {str(e)}")
                        result["media_files"] = []

                    result["success"] = True
                    result["status_code"] = navigation_result.get("status", 200)
                    result["final_url"] = navigation_result.get("final_url", url)

                else:
                    result["error"] = navigation_result.get("error", "Navigation failed")

                # Close page
                handler.close_page(page)

        except Exception as e:
            result["error"] = f"PlaywrightHandler error: {str(e)}"
            logger.error(f"Error in PlaywrightHandler crawl: {str(e)}")

        return result

    def get_playwright_handler_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái PlaywrightHandler.

        Returns:
            Dict[str, Any]: Thông tin PlaywrightHandler
        """
        return {
            "available": playwright_handler_available,
            "enabled": self.use_playwright_handler,
            "initialized": self.playwright_handler is not None,
            "configuration": {
                "headless": self.playwright_headless,
                "browser_type": self.playwright_browser_type,
                "timeout": self.playwright_timeout,
                "viewport": self.playwright_viewport,
                "user_agent": self.playwright_user_agent,
                "proxy": self.playwright_proxy
            } if self.playwright_handler else None
        }

    def create_playwright_context(self, **kwargs) -> Any:
        """
        Tạo context mới cho PlaywrightHandler.

        Args:
            **kwargs: Các tham số cho context

        Returns:
            Context object hoặc None nếu không có PlaywrightHandler
        """
        if not self.playwright_handler:
            return None

        try:
            return self.playwright_handler.create_context(**kwargs)
        except Exception as e:
            logger.error(f"Error creating Playwright context: {str(e)}")
            return None

    def extract_content_with_playwright(self, url: str, selectors: List[str] = None) -> Dict[str, Any]:
        """
        Trích xuất nội dung cụ thể từ URL sử dụng PlaywrightHandler.

        Args:
            url: URL cần trích xuất
            selectors: Danh sách CSS selectors để trích xuất nội dung cụ thể

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        if not self.playwright_handler:
            return {
                "success": False,
                "error": "PlaywrightHandler not available"
            }

        result = {
            "url": url,
            "success": False,
            "content": {},
            "error": None
        }

        try:
            with self.playwright_handler as handler:
                handler.setup()
                page = handler.create_page()

                # Navigate to URL
                navigation_result = handler.navigate(page, url)

                if navigation_result.get("success", False):
                    # Extract general content
                    content = handler.extract_content(page)
                    result["content"]["general"] = content

                    # Extract specific content using selectors
                    if selectors:
                        specific_content = {}
                        for selector in selectors:
                            try:
                                elements = page.evaluate(f"""() => {{
                                    const elements = document.querySelectorAll('{selector}');
                                    return Array.from(elements).map(el => {{
                                        return {{
                                            text: el.innerText,
                                            html: el.innerHTML,
                                            attributes: Object.fromEntries(
                                                Array.from(el.attributes).map(attr => [attr.name, attr.value])
                                            )
                                        }};
                                    }});
                                }}""")

                                specific_content[selector] = elements
                            except Exception as e:
                                logger.warning(f"Error extracting content for selector '{selector}': {str(e)}")
                                specific_content[selector] = []

                        result["content"]["specific"] = specific_content

                    result["success"] = True
                else:
                    result["error"] = navigation_result.get("error", "Navigation failed")

                handler.close_page(page)

        except Exception as e:
            result["error"] = f"Error in content extraction: {str(e)}"
            logger.error(f"Error in Playwright content extraction: {str(e)}")

        return result

    # ===== FILE PROCESSOR METHODS (from FileProcessor) =====

    def get_file_processor_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái FileProcessor.

        Returns:
            Dict[str, Any]: Thông tin FileProcessor
        """
        return {
            "available": file_processor_available,
            "enabled": self.use_file_processor,
            "configuration": {
                "encoding": self.file_processor_encoding,
                "chunk_size": self.file_processor_chunk_size,
                "extract_metadata": self.file_processor_extract_metadata,
                "extract_images": self.file_processor_extract_images,
                "ocr_enabled": self.file_processor_ocr_enabled,
                "language": self.file_processor_language,
                "max_file_size": self.file_processor_max_file_size,
                "timeout": self.file_processor_timeout
            } if self.use_file_processor else None
        }

    def process_file(
        self,
        url: str,
        output_path: Optional[str] = None,
        force_refresh: bool = False,
        extract_text: bool = True,
        extract_metadata: bool = True,
        extract_images: bool = False,
        extract_tables: bool = False,
        extract_charts: bool = False,
        extract_formulas: bool = False,
        extract_notes: bool = False,
        max_content_length: int = 10000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Tải xuống và phân tích file từ URL.

        Args:
            url: URL của file
            output_path: Đường dẫn để lưu file
            force_refresh: Có bắt buộc tải lại file không
            extract_text: Có trích xuất văn bản không
            extract_metadata: Có trích xuất metadata không
            extract_images: Có trích xuất hình ảnh không
            extract_tables: Có trích xuất bảng không
            extract_charts: Có trích xuất biểu đồ không
            extract_formulas: Có trích xuất công thức không
            extract_notes: Có trích xuất ghi chú không
            max_content_length: Độ dài tối đa của nội dung
            **kwargs: Các tham số tùy chọn

        Returns:
            Dict[str, Any]: Kết quả phân tích file
        """
        if not self.use_file_processor or not file_processor_available:
            return {
                "success": False,
                "error": "FileProcessor không khả dụng",
                "url": url
            }

        try:
            if hasattr(self, '_file_processor') and self._file_processor:
                return self._file_processor.process_file(
                    url=url,
                    output_path=output_path,
                    force_refresh=force_refresh,
                    extract_text=extract_text,
                    extract_metadata=extract_metadata,
                    extract_images=extract_images,
                    extract_tables=extract_tables,
                    extract_charts=extract_charts,
                    extract_formulas=extract_formulas,
                    extract_notes=extract_notes,
                    max_content_length=max_content_length,
                    **kwargs
                )
            else:
                # Fallback implementation
                download_result = self.download_file(url, output_path)
                if not download_result.get("success"):
                    return {
                        "success": False,
                        "error": download_result.get("error", "Lỗi khi tải xuống file"),
                        "url": url
                    }

                # Extract content from downloaded file
                file_path = download_result.get("file_path")
                if file_path:
                    extract_result = self.extract_content_from_file(file_path)
                    return {
                        "success": extract_result.get("success", False),
                        "url": url,
                        "file_path": file_path,
                        "text": extract_result.get("text", ""),
                        "metadata": extract_result.get("metadata", {}),
                        "error": extract_result.get("error", "")
                    }
                else:
                    return {
                        "success": False,
                        "error": "Không thể lấy đường dẫn file",
                        "url": url
                    }

        except Exception as e:
            logger.error(f"Lỗi khi xử lý file từ {url}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }

    def extract_content_from_file(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file local.

        Args:
            file_path: Đường dẫn đến file
            **kwargs: Các tham số tùy chọn

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        if not self.use_file_processor:
            return {
                "success": False,
                "error": "FileProcessor bị tắt",
                "file_path": file_path,
                "text": ""
            }

        try:
            if hasattr(self, '_file_processor') and self._file_processor:
                # Use integrated file processor
                if hasattr(self._file_processor, 'extract_content'):
                    return self._file_processor.extract_content(file_path, **kwargs)
                elif hasattr(self._file_processor, 'extract_file_content'):
                    return self._file_processor.extract_file_content(file_path, **kwargs)
            
            # Fallback implementation
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": "File không tồn tại",
                    "file_path": file_path,
                    "text": ""
                }

            # Basic text extraction based on file extension
            file_ext = os.path.splitext(file_path)[1].lower()
            text_content = ""
            metadata = {}

            if file_ext in ['.txt', '.md', '.markdown', '.csv', '.json', '.xml', '.html', '.htm']:
                # Text files
                try:
                    with open(file_path, 'r', encoding=self.file_processor_encoding) as f:
                        text_content = f.read()
                except UnicodeDecodeError:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            text_content = f.read()
                    except UnicodeDecodeError:
                        with open(file_path, 'r', encoding='latin-1') as f:
                            text_content = f.read()
            
            # Get file metadata
            stat_info = os.stat(file_path)
            metadata = {
                "file_name": os.path.basename(file_path),
                "file_size": stat_info.st_size,
                "file_extension": file_ext,
                "created": stat_info.st_ctime,
                "modified": stat_info.st_mtime
            }

            return {
                "success": True,
                "file_path": file_path,
                "text": text_content,
                "metadata": metadata
            }

        except Exception as e:
            logger.error(f"Lỗi khi trích xuất nội dung từ file {file_path}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path,
                "text": ""
            }

    def download_file(
        self,
        url: str,
        output_path: Optional[str] = None,
        use_playwright: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Tải xuống file từ URL.

        Args:
            url: URL của file
            output_path: Đường dẫn để lưu file
            use_playwright: Có sử dụng Playwright hay không
            **kwargs: Các tham số tùy chọn

        Returns:
            Dict[str, Any]: Thông tin về file đã tải xuống
        """
        if not self.use_file_processor:
            return {
                "success": False,
                "error": "FileProcessor bị tắt",
                "url": url
            }

        try:
            if hasattr(self, '_file_processor') and self._file_processor:
                return self._file_processor.download_file(url, output_path, use_playwright, **kwargs)
            
            # Fallback implementation
            import requests
            import time
            from urllib.parse import urlparse, unquote
            
            # Create output path if not provided
            if not output_path:
                parsed_url = urlparse(url)
                file_name = os.path.basename(unquote(parsed_url.path))
                if not file_name or '.' not in file_name:
                    file_name = f"file_{hash(url) % 1000000}"
                
                # Create downloads directory if needed
                downloads_dir = os.path.join(os.getcwd(), "downloads")
                os.makedirs(downloads_dir, exist_ok=True)
                output_path = os.path.join(downloads_dir, file_name)

            # Initialize result
            result = {
                "success": False,
                "url": url,
                "file_path": output_path,
                "file_name": os.path.basename(output_path),
                "content_type": None,
                "size": 0,
                "download_time": time.time()
            }

            # Download file using requests
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(
                url,
                headers=headers,
                stream=True,
                timeout=self.file_processor_timeout
            )

            # Check status
            if response.status_code != 200:
                result["error"] = f"HTTP error: {response.status_code}"
                return result

            # Get Content-Type
            content_type = response.headers.get('Content-Type', '')
            result["content_type"] = content_type

            # Check file size
            content_length = int(response.headers.get('Content-Length', 0))
            if content_length > self.file_processor_max_file_size:
                result["error"] = f"File quá lớn: {content_length} bytes"
                return result

            # Create directory if needed
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Save file
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=self.file_processor_chunk_size):
                    if chunk:
                        f.write(chunk)

            # Update size
            result["size"] = os.path.getsize(output_path)
            result["success"] = True

            logger.info(f"Đã tải xuống file: {result['file_name']} ({result['size']} bytes)")
            return result

        except Exception as e:
            logger.error(f"Lỗi khi tải xuống file từ {url}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }

    def is_file_url(self, url: str) -> bool:
        """
        Kiểm tra xem URL có phải là liên kết đến file hay không.

        Args:
            url: URL cần kiểm tra

        Returns:
            bool: True nếu là file, False nếu không
        """
        if not self.use_file_processor:
            return False

        try:
            if hasattr(self, '_file_processor') and self._file_processor:
                if hasattr(self._file_processor, 'is_file_url'):
                    return self._file_processor.is_file_url(url)
            
            # Fallback implementation
            from urllib.parse import urlparse, unquote
            
            # Check URL extension
            parsed_url = urlparse(url)
            path = unquote(parsed_url.path)
            
            # List of common file extensions
            file_extensions = [
                '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
                '.txt', '.csv', '.json', '.xml', '.html', '.htm',
                '.zip', '.rar', '.tar', '.gz', '.7z',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
                '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
                '.epub', '.mobi', '.djvu', '.chm'
            ]
            
            # Check extension
            for ext in file_extensions:
                if path.lower().endswith(ext):
                    return True
            
            # Check if query parameters contain file indicators
            query = parsed_url.query.lower()
            if 'download' in query or 'file' in query:
                return True
            
            # Check special URL patterns
            special_patterns = ['/download/', '/files/', '/documents/', '/attachments/']
            for pattern in special_patterns:
                if pattern in url.lower():
                    return True
            
            return False

        except Exception as e:
            logger.error(f"Lỗi khi kiểm tra file URL {url}: {str(e)}")
            return False

    # ===== CONTENT EXTRACTION UTILS METHODS (from Content Extraction Utils) =====

    def get_content_extraction_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái Content Extraction Utils.

        Returns:
            Dict[str, Any]: Thông tin Content Extraction Utils
        """
        return {
            "available": content_extraction_utils_available,
            "enabled": self.use_content_extraction,
            "configuration": {
                "min_length": self.content_min_length,
                "max_length": self.content_max_length,
                "remove_noise": self.content_remove_noise,
                "extract_metadata": self.content_extract_metadata,
                "extract_links": self.content_extract_links,
                "extract_images": self.content_extract_images,
                "extract_tables": self.content_extract_tables,
                "clean_html": self.content_clean_html,
                "preserve_structure": self.content_preserve_structure,
                "extract_structured": self.content_extract_structured,
                "site_detection": self.content_site_detection,
                "custom_selectors": self.content_custom_selectors,
                "blacklist_selectors": self.content_blacklist_selectors,
                "summary_enabled": self.content_summary_enabled,
                "summary_max_length": self.content_summary_max_length,
                "summary_method": self.content_summary_method
            } if self.use_content_extraction else None
        }

    def extract_content_advanced(self, url: str, html: str, **kwargs) -> Dict[str, Any]:
        """
        Trích xuất nội dung nâng cao từ HTML sử dụng Content Extraction Utils.

        Args:
            url: URL của trang web
            html: HTML content
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        if not self.use_content_extraction or not content_extraction_utils_available:
            return {
                "success": False,
                "error": "Content Extraction Utils không khả dụng",
                "url": url,
                "text": "",
                "title": "",
                "metadata": {}
            }

        result = {
            "url": url,
            "success": False,
            "text": "",
            "title": "",
            "metadata": {},
            "images": [],
            "tables": [],
            "structured_data": {},
            "site_type": "general",
            "summary": "",
            "error": None
        }

        try:
            # Detect site type if enabled
            if self.content_site_detection:
                site_type = detect_site_type(url, html)
                result["site_type"] = site_type

            # Extract structured content if enabled
            if self.content_extract_structured:
                structured_result = extract_structured_content(html, url)
                result.update(structured_result)
            else:
                # Extract main content
                main_content = extract_main_content(html, url)
                result["text"] = main_content

                # Extract title from HTML
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html, "html.parser")
                title = soup.title.text.strip() if soup.title else ""
                result["title"] = title

            # Extract content with custom selectors if provided
            if self.content_custom_selectors:
                custom_content = {}
                for selector_name, selector in self.content_custom_selectors.items():
                    custom_content[selector_name] = extract_content_with_selector(html, selector)
                result["custom_content"] = custom_content

            # Generate summary if enabled
            if self.content_summary_enabled and result["text"]:
                summary = summarize_content(
                    result["text"],
                    max_length=self.content_summary_max_length
                )
                result["summary"] = summary

            # Filter content by length
            if result["text"]:
                text_length = len(result["text"])
                if text_length < self.content_min_length:
                    result["warning"] = f"Nội dung quá ngắn: {text_length} ký tự"
                elif text_length > self.content_max_length:
                    result["text"] = result["text"][:self.content_max_length]
                    result["warning"] = f"Nội dung bị cắt ngắn từ {text_length} xuống {self.content_max_length} ký tự"

            result["success"] = True

        except Exception as e:
            result["error"] = f"Lỗi khi trích xuất nội dung: {str(e)}"
            logger.error(f"Error in content extraction for {url}: {str(e)}")

        return result

    # ===== ADVANCED MONITORING METHODS (from Advanced Monitoring) =====

    def get_monitoring_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái Advanced Monitoring.

        Returns:
            Dict[str, Any]: Thông tin Advanced Monitoring
        """
        return {
            "available": advanced_monitoring_available,
            "enabled": self.use_advanced_monitoring,
            "configuration": {
                "monitoring_enabled": self.monitoring_enabled,
                "system_monitoring_enabled": self.system_monitoring_enabled,
                "system_monitoring_interval": self.system_monitoring_interval,
                "performance_tracking_enabled": self.performance_tracking_enabled,
                "memory_monitoring_enabled": self.memory_monitoring_enabled,
                "cpu_monitoring_enabled": self.cpu_monitoring_enabled,
                "network_monitoring_enabled": self.network_monitoring_enabled,
                "cache_monitoring_enabled": self.cache_monitoring_enabled,
                "error_tracking_enabled": self.error_tracking_enabled,
                "execution_time_tracking": self.execution_time_tracking,
                "monitoring_auto_start": self.monitoring_auto_start,
                "monitoring_save_to_file": self.monitoring_save_to_file,
                "monitoring_file_path": self.monitoring_file_path
            } if self.use_advanced_monitoring else None,
            "performance_metrics_active": self.performance_metrics is not None,
            "system_monitor_active": self.system_monitor is not None,
            "system_monitor_running": (
                self.system_monitor.running if self.system_monitor else False
            )
        }

    def start_monitoring(self) -> bool:
        """
        Bắt đầu system monitoring.

        Returns:
            bool: True nếu thành công
        """
        if not self.use_advanced_monitoring or not self.system_monitor:
            return False

        try:
            self.system_monitor.start_monitoring()
            logger.info("System monitoring started")
            return True
        except Exception as e:
            logger.error(f"Failed to start monitoring: {str(e)}")
            return False

    def stop_monitoring(self) -> bool:
        """
        Dừng system monitoring.

        Returns:
            bool: True nếu thành công
        """
        if not self.use_advanced_monitoring or not self.system_monitor:
            return False

        try:
            self.system_monitor.stop_monitoring()
            logger.info("System monitoring stopped")
            return True
        except Exception as e:
            logger.error(f"Failed to stop monitoring: {str(e)}")
            return False

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Lấy performance metrics.

        Returns:
            Dict[str, Any]: Performance metrics
        """
        if not self.use_advanced_monitoring or not self.performance_metrics:
            return {"error": "Performance metrics not available"}

        try:
            return self.performance_metrics.get_summary()
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {str(e)}")
            return {"error": str(e)}

    def get_system_status(self) -> Dict[str, Any]:
        """
        Lấy system status.

        Returns:
            Dict[str, Any]: System status
        """
        if not self.use_advanced_monitoring or not self.system_monitor:
            return {"error": "System monitor not available"}

        try:
            return self.system_monitor.get_system_status()
        except Exception as e:
            logger.error(f"Failed to get system status: {str(e)}")
            return {"error": str(e)}

    def record_crawl_metrics(self, url: str, success: bool, duration: float, size: int = 0):
        """
        Ghi lại metrics cho crawl operation.

        Args:
            url: URL được crawl
            success: Thành công hay không
            duration: Thời gian thực hiện (giây)
            size: Kích thước response (bytes)
        """
        if not self.use_advanced_monitoring or not self.performance_metrics:
            return

        try:
            # Record execution time
            if self.execution_time_tracking:
                self.performance_metrics.record_execution_time("crawl", duration)

            # Record network request
            if self.network_monitoring_enabled:
                self.performance_metrics.record_network_request(url, size, success)

            # Record request success/failure
            if self.error_tracking_enabled:
                error_type = None if success else "crawl_error"
                self.performance_metrics.record_request(success, error_type)

            # Record memory usage
            if self.memory_monitoring_enabled:
                self.performance_metrics.record_memory_usage()

            # Record CPU usage
            if self.cpu_monitoring_enabled:
                self.performance_metrics.record_cpu_usage()

        except Exception as e:
            logger.warning(f"Failed to record crawl metrics: {str(e)}")

    # ===== USER AGENT MANAGER METHODS (from User Agent Manager) =====

    def get_user_agent_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái User Agent Manager.

        Returns:
            Dict[str, Any]: Thông tin User Agent Manager
        """
        return {
            "available": user_agent_manager_available,
            "enabled": self.use_user_agent_manager,
            "configuration": {
                "rotation_enabled": self.user_agent_rotation_enabled,
                "rotation_interval": self.user_agent_rotation_interval,
                "device_type": self.user_agent_device_type,
                "custom_user_agent": self.user_agent_custom,
                "file_path": self.user_agent_file_path,
                "auto_update": self.user_agent_auto_update,
                "update_interval_days": self.user_agent_update_interval_days,
                "cache_dir": self.user_agent_cache_dir
            } if self.use_user_agent_manager else None,
            "manager_active": self.user_agent_manager is not None,
            "current_user_agent": self._get_user_agent() if self.use_user_agent_manager else None
        }

    def get_random_user_agent(self, device_type: str = None) -> str:
        """
        Lấy random user agent.

        Args:
            device_type: Loại thiết bị (desktop, mobile, tablet, bot)

        Returns:
            str: User agent string
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            return self._get_user_agent()

        try:
            return self.user_agent_manager.get_random_user_agent(device_type or self.user_agent_device_type)
        except Exception as e:
            logger.warning(f"Failed to get random user agent: {str(e)}")
            return self._get_user_agent()

    def get_next_user_agent(self, device_type: str = None) -> str:
        """
        Lấy user agent tiếp theo trong rotation.

        Args:
            device_type: Loại thiết bị (desktop, mobile, tablet, bot)

        Returns:
            str: User agent string
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            return self._get_user_agent()

        try:
            return self.user_agent_manager.get_next_user_agent(device_type or self.user_agent_device_type)
        except Exception as e:
            logger.warning(f"Failed to get next user agent: {str(e)}")
            return self._get_user_agent()

    def add_user_agent(self, user_agent: str, device_type: str = "desktop") -> bool:
        """
        Thêm user agent mới.

        Args:
            user_agent: User agent string
            device_type: Loại thiết bị

        Returns:
            bool: True nếu thành công
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            return False

        try:
            self.user_agent_manager.add_user_agent(user_agent, device_type)
            logger.info(f"Added user agent for {device_type}: {user_agent[:50]}...")
            return True
        except Exception as e:
            logger.error(f"Failed to add user agent: {str(e)}")
            return False

    def remove_user_agent(self, user_agent: str, device_type: str = None) -> bool:
        """
        Xóa user agent.

        Args:
            user_agent: User agent string
            device_type: Loại thiết bị

        Returns:
            bool: True nếu thành công
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            return False

        try:
            result = self.user_agent_manager.remove_user_agent(user_agent, device_type)
            if result:
                logger.info(f"Removed user agent: {user_agent[:50]}...")
            return result
        except Exception as e:
            logger.error(f"Failed to remove user agent: {str(e)}")
            return False

    def get_user_agents_count(self, device_type: str = None) -> int:
        """
        Lấy số lượng user agents.

        Args:
            device_type: Loại thiết bị

        Returns:
            int: Số lượng user agents
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            return len(self.user_agents) if hasattr(self, 'user_agents') and self.user_agents else 1

        try:
            return self.user_agent_manager.get_user_agents_count(device_type)
        except Exception as e:
            logger.warning(f"Failed to get user agents count: {str(e)}")
            return 0

    def get_all_user_agents(self, device_type: str = None) -> List[str]:
        """
        Lấy tất cả user agents.

        Args:
            device_type: Loại thiết bị

        Returns:
            List[str]: Danh sách user agents
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            return self.user_agents if hasattr(self, 'user_agents') and self.user_agents else []

        try:
            return self.user_agent_manager.get_all_user_agents(device_type)
        except Exception as e:
            logger.warning(f"Failed to get all user agents: {str(e)}")
            return []

    def set_custom_user_agent(self, user_agent: str) -> bool:
        """
        Đặt custom user agent.

        Args:
            user_agent: User agent string

        Returns:
            bool: True nếu thành công
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            self.user_agent_custom = user_agent
            return True

        try:
            self.user_agent_manager.set_custom_user_agent(user_agent)
            self.user_agent_custom = user_agent
            logger.info(f"Set custom user agent: {user_agent[:50]}...")
            return True
        except Exception as e:
            logger.error(f"Failed to set custom user agent: {str(e)}")
            return False

    def enable_user_agent_rotation(self, interval: int = 10) -> bool:
        """
        Bật user agent rotation.

        Args:
            interval: Khoảng thời gian rotation (giây)

        Returns:
            bool: True nếu thành công
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            self.user_agent_rotation_enabled = True
            self.user_agent_rotation_interval = interval
            return True

        try:
            self.user_agent_manager.enable_rotation(interval)
            self.user_agent_rotation_enabled = True
            self.user_agent_rotation_interval = interval
            logger.info(f"Enabled user agent rotation with interval: {interval}s")
            return True
        except Exception as e:
            logger.error(f"Failed to enable user agent rotation: {str(e)}")
            return False

    def disable_user_agent_rotation(self) -> bool:
        """
        Tắt user agent rotation.

        Returns:
            bool: True nếu thành công
        """
        if not self.use_user_agent_manager or not self.user_agent_manager:
            self.user_agent_rotation_enabled = False
            return True

        try:
            self.user_agent_manager.disable_rotation()
            self.user_agent_rotation_enabled = False
            logger.info("Disabled user agent rotation")
            return True
        except Exception as e:
            logger.error(f"Failed to disable user agent rotation: {str(e)}")
            return False

    # ===== SITE STRUCTURE HANDLER METHODS (from Site Structure Handler) =====

    def get_site_structure_status(self) -> Dict[str, Any]:
        """
        Lấy thông tin trạng thái Site Structure Handler.

        Returns:
            Dict[str, Any]: Thông tin Site Structure Handler
        """
        return {
            "available": site_structure_handler_available,
            "enabled": self.use_site_structure_handler,
            "configuration": {
                "extract_navigation": self.site_structure_extract_navigation,
                "extract_breadcrumbs": self.site_structure_extract_breadcrumbs,
                "extract_pagination": self.site_structure_extract_pagination,
                "extract_forms": self.site_structure_extract_forms,
                "detect_page_type": self.site_structure_detect_page_type,
                "detect_site_type": self.site_structure_detect_site_type,
                "detect_language": self.site_structure_detect_language,
                "max_concurrent_requests": self.site_structure_max_concurrent_requests,
                "cache_enabled": self.site_structure_cache_enabled,
                "cache_ttl": self.site_structure_cache_ttl,
                "cache_size": self.site_structure_cache_size,
                "use_playwright": self.site_structure_use_playwright
            } if self.use_site_structure_handler else None,
            "handler_active": self.site_structure_handler is not None
        }

    def analyze_page_structure(self, url: str, content: str = None) -> Dict[str, Any]:
        """
        Phân tích cấu trúc của một trang web.

        Args:
            url: URL của trang
            content: Nội dung HTML của trang (nếu đã có)

        Returns:
            Dict[str, Any]: Thông tin cấu trúc trang
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return {"url": url, "page_type": "unknown", "site_type": "unknown"}

        try:
            return self.site_structure_handler.analyze_page(url, content)
        except Exception as e:
            logger.warning(f"Failed to analyze page structure: {str(e)}")
            return {"url": url, "page_type": "unknown", "site_type": "unknown", "error": str(e)}

    def extract_navigation_structure(self, url: str, content: str = None) -> List[Dict[str, Any]]:
        """
        Trích xuất cấu trúc điều hướng từ trang web.

        Args:
            url: URL của trang
            content: Nội dung HTML của trang (nếu đã có)

        Returns:
            List[Dict[str, Any]]: Danh sách các mục điều hướng
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return []

        try:
            return self.site_structure_handler.extract_navigation(url, content)
        except Exception as e:
            logger.warning(f"Failed to extract navigation structure: {str(e)}")
            return []

    def extract_breadcrumbs_structure(self, url: str, content: str = None) -> List[Dict[str, Any]]:
        """
        Trích xuất breadcrumbs từ trang web.

        Args:
            url: URL của trang
            content: Nội dung HTML của trang (nếu đã có)

        Returns:
            List[Dict[str, Any]]: Danh sách breadcrumbs
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return []

        try:
            return self.site_structure_handler.extract_breadcrumbs(url, content)
        except Exception as e:
            logger.warning(f"Failed to extract breadcrumbs structure: {str(e)}")
            return []

    def extract_pagination_structure(self, url: str, content: str = None) -> Dict[str, Any]:
        """
        Trích xuất thông tin phân trang từ trang web.

        Args:
            url: URL của trang
            content: Nội dung HTML của trang (nếu đã có)

        Returns:
            Dict[str, Any]: Thông tin phân trang
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return {}

        try:
            return self.site_structure_handler.extract_pagination(url, content)
        except Exception as e:
            logger.warning(f"Failed to extract pagination structure: {str(e)}")
            return {}

    def detect_page_type_advanced(self, url: str, content: str = None) -> str:
        """
        Phát hiện loại trang web nâng cao.

        Args:
            url: URL của trang
            content: Nội dung HTML của trang (nếu đã có)

        Returns:
            str: Loại trang (home, category, detail, search, login, etc.)
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return "unknown"

        try:
            return self.site_structure_handler.detect_page_type(url, content)
        except Exception as e:
            logger.warning(f"Failed to detect page type: {str(e)}")
            return "unknown"

    def detect_site_type_advanced(self, url: str, content: str = None) -> str:
        """
        Phát hiện loại trang web nâng cao.

        Args:
            url: URL của trang
            content: Nội dung HTML của trang (nếu đã có)

        Returns:
            str: Loại trang web (blog, ecommerce, news, etc.)
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return "unknown"

        try:
            return self.site_structure_handler.detect_site_type(url, content)
        except Exception as e:
            logger.warning(f"Failed to detect site type: {str(e)}")
            return "unknown"

    def build_site_structure_map(self, start_url: str, max_depth: int = None, max_urls: int = None) -> Dict[str, Any]:
        """
        Xây dựng bản đồ cấu trúc trang web.

        Args:
            start_url: URL bắt đầu
            max_depth: Độ sâu tối đa
            max_urls: Số lượng URL tối đa

        Returns:
            Dict[str, Any]: Bản đồ cấu trúc trang web
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return {}

        try:
            return self.site_structure_handler.build_site_structure(start_url, max_depth, max_urls)
        except Exception as e:
            logger.warning(f"Failed to build site structure map: {str(e)}")
            return {}

    def get_site_structure_map(self) -> Dict[str, Any]:
        """
        Lấy bản đồ cấu trúc trang web hiện tại.

        Returns:
            Dict[str, Any]: Bản đồ cấu trúc trang web
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return {}

        try:
            return self.site_structure_handler.get_site_structure()
        except Exception as e:
            logger.warning(f"Failed to get site structure map: {str(e)}")
            return {}

    def analyze_site_structure(self) -> Dict[str, Any]:
        """
        Phân tích cấu trúc trang web.

        Returns:
            Dict[str, Any]: Kết quả phân tích cấu trúc
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return {}

        try:
            return self.site_structure_handler.analyze_structure()
        except Exception as e:
            logger.warning(f"Failed to analyze site structure: {str(e)}")
            return {}

    def find_site_patterns(self) -> Dict[str, Any]:
        """
        Tìm các mẫu phổ biến trong cấu trúc trang web.

        Returns:
            Dict[str, Any]: Các mẫu được tìm thấy
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            return {}

        try:
            return self.site_structure_handler.find_common_patterns()
        except Exception as e:
            logger.warning(f"Failed to find site patterns: {str(e)}")
            return {}

    def crawl_with_site_structure_analysis(self, start_url: str, max_depth: int = None, max_urls: int = None) -> Dict[str, Any]:
        """
        Crawl trang web với phân tích cấu trúc trang web.

        Args:
            start_url: URL bắt đầu
            max_depth: Độ sâu tối đa
            max_urls: Số lượng URL tối đa

        Returns:
            Dict[str, Any]: Kết quả crawl với thông tin cấu trúc
        """
        if not self.use_site_structure_handler or not self.site_structure_handler:
            logger.warning("Site Structure Handler not available, falling back to regular crawl")
            return self.crawl_single_url(start_url)

        try:
            # Crawl với Site Structure Handler
            def html_fetcher(url):
                """Hàm fetcher HTML cho Site Structure Handler"""
                result = self._crawl_single_page(url)
                if result.get("success") and result.get("html"):
                    return {
                        "html": result["html"],
                        "url": url,
                        "success": True,
                        "status_code": 200,
                        "content_type": "text/html"
                    }
                else:
                    return {
                        "html": None,
                        "url": url,
                        "success": False,
                        "error": result.get("error", "Failed to fetch HTML")
                    }

            # Sử dụng Site Structure Handler để crawl
            self.site_structure_handler.crawl(start_url=start_url, html_fetcher=html_fetcher)

            # Lấy kết quả cấu trúc
            structure = self.site_structure_handler.get_site_structure()
            analysis = self.site_structure_handler.analyze_structure()
            patterns = self.site_structure_handler.find_common_patterns()

            # Tạo kết quả tổng hợp
            result = {
                "url": start_url,
                "success": True,
                "timestamp": time.time(),
                "site_structure": {
                    "structure": structure,
                    "analysis": analysis,
                    "patterns": patterns
                },
                "crawl_summary": {
                    "pages_crawled": len(structure.get("nodes", [])),
                    "links_found": len(structure.get("edges", [])),
                    "max_depth_reached": analysis.get("basic_stats", {}).get("max_depth", 0),
                    "site_type": patterns.get("site_type", "unknown"),
                    "page_types": patterns.get("page_types", {}),
                    "navigation_structure": patterns.get("navigation", {}),
                    "pagination_detected": patterns.get("pagination", {}).get("detected", False)
                }
            }

            logger.info(f"Site structure crawl completed for {start_url}")
            logger.info(f"Pages crawled: {result['crawl_summary']['pages_crawled']}")
            logger.info(f"Links found: {result['crawl_summary']['links_found']}")
            logger.info(f"Site type: {result['crawl_summary']['site_type']}")

            return result

        except Exception as e:
            logger.error(f"Failed to crawl with site structure analysis: {str(e)}")
            return {
                "url": start_url,
                "success": False,
                "error": str(e),
                "timestamp": time.time(),
                "fallback_used": True
            }

    def get_site_structure_handler_status(self) -> Dict[str, Any]:
        """
        Lấy trạng thái của Site Structure Handler.

        Returns:
            Dict[str, Any]: Thông tin trạng thái Site Structure Handler
        """
        return {
            "available": site_structure_handler_available,
            "enabled": self.use_site_structure_handler,
            "initialized": self.site_structure_handler is not None,
            "configuration": {
                "extract_navigation": self.site_structure_extract_navigation,
                "extract_breadcrumbs": self.site_structure_extract_breadcrumbs,
                "extract_pagination": self.site_structure_extract_pagination,
                "extract_forms": self.site_structure_extract_forms,
                "detect_page_type": self.site_structure_detect_page_type,
                "detect_site_type": self.site_structure_detect_site_type,
                "detect_language": self.site_structure_detect_language,
                "respect_robots": self.site_structure_respect_robots,
                "use_sitemap": self.site_structure_use_sitemap,
                "max_depth": self.site_structure_max_depth,
                "max_urls": self.site_structure_max_urls,
                "max_urls_per_domain": self.site_structure_max_urls_per_domain,
                "max_concurrent_requests": self.site_structure_max_concurrent_requests,
                "cache_enabled": self.site_structure_cache_enabled,
                "cache_ttl": self.site_structure_cache_ttl,
                "cache_size": self.site_structure_cache_size,
                "use_playwright": self.site_structure_use_playwright
            } if self.use_site_structure_handler else None,
            "handler_active": self.site_structure_handler is not None,
            "methods_available": [
                "analyze_page_structure",
                "extract_navigation_structure",
                "extract_breadcrumbs_structure",
                "extract_pagination_structure",
                "detect_page_type_advanced",
                "detect_site_type_advanced",
                "build_site_structure_map",
                "get_site_structure_map",
                "analyze_site_structure",
                "find_site_patterns",
                "crawl_with_site_structure_analysis"
            ] if self.site_structure_handler else []
        }

    # ==================== LANGUAGE HANDLER METHODS ====================

    def detect_text_language(self, text: str) -> str:
        """
        Phát hiện ngôn ngữ của văn bản.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ

        Returns:
            str: Mã ngôn ngữ (ví dụ: 'vi', 'en')
        """
        if not self.use_language_handler or not self.language_handler:
            # Fallback to simple detection
            return detect_language(text)

        try:
            return self.language_handler.detect_language(text)
        except Exception as e:
            logger.warning(f"Failed to detect language: {str(e)}")
            return self.language_default_lang

    def is_vietnamese_text_check(self, text: str) -> bool:
        """
        Kiểm tra xem văn bản có phải tiếng Việt hay không.

        Args:
            text: Văn bản cần kiểm tra

        Returns:
            bool: True nếu là tiếng Việt
        """
        if not self.use_language_handler or not self.language_handler:
            # Fallback to simple check
            return is_vietnamese_text(text)

        try:
            detected_lang = self.language_handler.detect_language(text)
            return detected_lang == 'vi'
        except Exception as e:
            logger.warning(f"Failed to check Vietnamese text: {str(e)}")
            return False

    def remove_vietnamese_tones_text(self, text: str) -> str:
        """
        Loại bỏ dấu tiếng Việt khỏi văn bản.

        Args:
            text: Văn bản tiếng Việt có dấu

        Returns:
            str: Văn bản tiếng Việt không dấu
        """
        if not self.use_language_handler or not self.language_handler:
            # Fallback to simple function
            return remove_vietnamese_tones(text)

        try:
            return self.language_handler.remove_vietnamese_tones(text)
        except Exception as e:
            logger.warning(f"Failed to remove Vietnamese tones: {str(e)}")
            return text

    def normalize_vietnamese_text_content(self, text: str) -> str:
        """
        Chuẩn hóa văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần chuẩn hóa

        Returns:
            str: Văn bản tiếng Việt đã chuẩn hóa
        """
        if not self.use_language_handler or not self.language_handler:
            # Fallback to simple function
            return normalize_vietnamese_text(text)

        try:
            return self.language_handler.normalize_vietnamese_text(text)
        except Exception as e:
            logger.warning(f"Failed to normalize Vietnamese text: {str(e)}")
            return text

    def extract_keywords_from_text(self, text: str, language: str = None, max_keywords: int = None) -> List[str]:
        """
        Trích xuất từ khóa từ văn bản.

        Args:
            text: Văn bản cần trích xuất từ khóa
            language: Ngôn ngữ của văn bản (tự động phát hiện nếu None)
            max_keywords: Số lượng từ khóa tối đa

        Returns:
            List[str]: Danh sách từ khóa
        """
        if not self.use_language_handler or not self.language_handler:
            return []

        try:
            if language is None:
                language = self.detect_text_language(text)

            if max_keywords is None:
                max_keywords = self.language_max_keywords

            return self.language_handler.extract_keywords(text, language, max_keywords)
        except Exception as e:
            logger.warning(f"Failed to extract keywords: {str(e)}")
            return []

    def split_text_into_sentences(self, text: str, language: str = None) -> List[str]:
        """
        Chia văn bản thành các câu.

        Args:
            text: Văn bản cần chia
            language: Ngôn ngữ của văn bản (tự động phát hiện nếu None)

        Returns:
            List[str]: Danh sách các câu
        """
        if not self.use_language_handler or not self.language_handler:
            # Simple fallback
            return [text] if text else []

        try:
            if language is None:
                language = self.detect_text_language(text)

            return self.language_handler.split_into_sentences(text, language)
        except Exception as e:
            logger.warning(f"Failed to split into sentences: {str(e)}")
            return [text] if text else []

    def clean_vietnamese_text_content(self, text: str) -> str:
        """
        Làm sạch văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần làm sạch

        Returns:
            str: Văn bản tiếng Việt đã làm sạch
        """
        if not self.use_language_handler or not self.language_handler:
            return text

        try:
            return self.language_handler.clean_vietnamese_text(text)
        except Exception as e:
            logger.warning(f"Failed to clean Vietnamese text: {str(e)}")
            return text

    def get_language_name_from_code(self, language_code: str) -> str:
        """
        Lấy tên ngôn ngữ từ mã ngôn ngữ.

        Args:
            language_code: Mã ngôn ngữ (ví dụ: 'vi', 'en')

        Returns:
            str: Tên ngôn ngữ
        """
        if not self.use_language_handler or not self.language_handler:
            return f"Unknown ({language_code})"

        try:
            return self.language_handler.get_language_name(language_code)
        except Exception as e:
            logger.warning(f"Failed to get language name: {str(e)}")
            return f"Unknown ({language_code})"

    def process_text_with_language_handler(self, text: str, operations: List[str] = None) -> Dict[str, Any]:
        """
        Xử lý văn bản với Language Handler với nhiều thao tác.

        Args:
            text: Văn bản cần xử lý
            operations: Danh sách các thao tác cần thực hiện

        Returns:
            Dict[str, Any]: Kết quả xử lý văn bản
        """
        if not self.use_language_handler or not self.language_handler:
            return {
                "original_text": text,
                "language": self.language_default_lang,
                "processed": False,
                "error": "Language Handler not available"
            }

        if operations is None:
            operations = []
            if self.language_auto_detect_enabled:
                operations.append("detect_language")
            if self.language_vietnamese_processing and self.language_normalize_text:
                operations.append("normalize_vietnamese")
            if self.language_remove_tones:
                operations.append("remove_tones")
            if self.language_extract_keywords:
                operations.append("extract_keywords")
            if self.language_split_sentences:
                operations.append("split_sentences")
            if self.language_clean_vietnamese:
                operations.append("clean_vietnamese")

        result = {
            "original_text": text,
            "processed_text": text,
            "language": self.language_default_lang,
            "operations_performed": [],
            "processed": True,
            "metadata": {}
        }

        try:
            current_text = text

            # Detect language
            if "detect_language" in operations:
                detected_lang = self.detect_text_language(current_text)
                result["language"] = detected_lang
                result["operations_performed"].append("detect_language")
                result["metadata"]["detected_language"] = detected_lang

            # Filter by language if enabled
            if self.language_filter_by_language and result["language"] not in self.language_target_languages:
                result["filtered_out"] = True
                result["filter_reason"] = f"Language '{result['language']}' not in target languages {self.language_target_languages}"
                return result

            # Vietnamese processing
            if result["language"] == "vi" and self.language_vietnamese_processing:

                # Normalize Vietnamese text
                if "normalize_vietnamese" in operations:
                    current_text = self.normalize_vietnamese_text_content(current_text)
                    result["operations_performed"].append("normalize_vietnamese")

                # Remove Vietnamese tones
                if "remove_tones" in operations:
                    no_tones_text = self.remove_vietnamese_tones_text(current_text)
                    result["metadata"]["text_without_tones"] = no_tones_text
                    result["operations_performed"].append("remove_tones")

                # Clean Vietnamese text
                if "clean_vietnamese" in operations:
                    current_text = self.clean_vietnamese_text_content(current_text)
                    result["operations_performed"].append("clean_vietnamese")

            # Extract keywords
            if "extract_keywords" in operations:
                keywords = self.extract_keywords_from_text(current_text, result["language"])
                result["metadata"]["keywords"] = keywords
                result["operations_performed"].append("extract_keywords")

            # Split into sentences
            if "split_sentences" in operations:
                sentences = self.split_text_into_sentences(current_text, result["language"])
                result["metadata"]["sentences"] = sentences
                result["metadata"]["sentence_count"] = len(sentences)
                result["operations_performed"].append("split_sentences")

            result["processed_text"] = current_text
            result["language_name"] = self.get_language_name_from_code(result["language"])

        except Exception as e:
            logger.error(f"Failed to process text with Language Handler: {str(e)}")
            result["processed"] = False
            result["error"] = str(e)

        return result

    def get_language_handler_status(self) -> Dict[str, Any]:
        """
        Lấy trạng thái của Language Handler.

        Returns:
            Dict[str, Any]: Thông tin trạng thái Language Handler
        """
        return {
            "available": language_handler_available,
            "enabled": self.use_language_handler,
            "initialized": self.language_handler is not None,
            "configuration": {
                "default_lang": self.language_default_lang,
                "detection_method": self.language_detection_method,
                "fasttext_model_path": self.language_fasttext_model_path,
                "min_text_length": self.language_min_text_length,
                "verbose": self.language_verbose,
                "auto_detect_enabled": self.language_auto_detect_enabled,
                "vietnamese_processing": self.language_vietnamese_processing,
                "remove_tones": self.language_remove_tones,
                "normalize_text": self.language_normalize_text,
                "extract_keywords": self.language_extract_keywords,
                "max_keywords": self.language_max_keywords,
                "split_sentences": self.language_split_sentences,
                "clean_vietnamese": self.language_clean_vietnamese,
                "target_languages": self.language_target_languages,
                "filter_by_language": self.language_filter_by_language
            } if self.use_language_handler else None,
            "handler_active": self.language_handler is not None,
            "methods_available": [
                "detect_text_language",
                "is_vietnamese_text_check",
                "remove_vietnamese_tones_text",
                "normalize_vietnamese_text_content",
                "extract_keywords_from_text",
                "split_text_into_sentences",
                "clean_vietnamese_text_content",
                "get_language_name_from_code",
                "process_text_with_language_handler"
            ] if self.language_handler else []
        }

    # ==================== INTEGRATION MANAGER METHODS ====================

    def detect_integrated_modules(self) -> Dict[str, bool]:
        """
        Phát hiện các module đã được tích hợp.

        Returns:
            Dict[str, bool]: Trạng thái tích hợp của các module
        """
        if not self.use_integration_manager or not self.integration_manager:
            return {}

        try:
            self.integration_manager.detect_modules()
            return {
                module: module in self.integration_manager.get_integrated_modules()
                for module in [
                    "captcha_handler", "user_agent_manager", "file_processor",
                    "playwright_handler", "site_structure_handler", "pagination_handler",
                    "language_handler", "config_manager"
                ]
            }
        except Exception as e:
            logger.warning(f"Failed to detect integrated modules: {str(e)}")
            return {}

    def integrate_single_module(self, module_name: str, module_config: Dict[str, Any] = None) -> bool:
        """
        Tích hợp một module đơn lẻ.

        Args:
            module_name: Tên module cần tích hợp
            module_config: Cấu hình cho module

        Returns:
            bool: True nếu tích hợp thành công
        """
        if not self.use_integration_manager or not self.integration_manager:
            logger.warning("Integration Manager not available")
            return False

        try:
            return self.integration_manager.integrate_module(module_name, module_config)
        except Exception as e:
            logger.error(f"Failed to integrate module {module_name}: {str(e)}")
            return False

    def integrate_all_modules_manual(self, config: Dict[str, Any] = None) -> Dict[str, bool]:
        """
        Tích hợp tất cả các module theo cách thủ công.

        Args:
            config: Cấu hình cho các module

        Returns:
            Dict[str, bool]: Kết quả tích hợp của từng module
        """
        if not self.use_integration_manager or not self.integration_manager:
            logger.warning("Integration Manager not available")
            return {}

        try:
            if config:
                self.integration_manager.config.update(config)

            results = self.integration_manager.integrate_all_modules()
            self.all_modules_integrated = True
            return results
        except Exception as e:
            logger.error(f"Failed to integrate all modules: {str(e)}")
            return {}

    def get_module_integration_status(self) -> Dict[str, Any]:
        """
        Lấy trạng thái tích hợp của các module.

        Returns:
            Dict[str, Any]: Thông tin trạng thái tích hợp
        """
        if not self.use_integration_manager or not self.integration_manager:
            return {
                "available": integration_manager_available,
                "enabled": self.use_integration_manager,
                "initialized": False,
                "all_modules_integrated": False,
                "integrated_modules": [],
                "failed_modules": [],
                "module_status": {}
            }

        try:
            return {
                "available": integration_manager_available,
                "enabled": self.use_integration_manager,
                "initialized": self.integration_manager is not None,
                "all_modules_integrated": self.all_modules_integrated,
                "integrated_modules": list(self.integration_manager.get_integrated_modules()),
                "failed_modules": list(self.integration_manager.get_failed_modules()),
                "module_status": self.integration_manager.get_module_status(),
                "configuration": {
                    "auto_integrate": self.integration_auto_integrate,
                    "required_modules": self.integration_required_modules,
                    "optional_modules": self.integration_optional_modules,
                    "fallback_enabled": self.integration_fallback_enabled,
                    "verbose": self.integration_verbose,
                    "config": self.integration_config
                }
            }
        except Exception as e:
            logger.error(f"Failed to get module integration status: {str(e)}")
            return {
                "available": integration_manager_available,
                "enabled": self.use_integration_manager,
                "initialized": False,
                "error": str(e)
            }

    def get_integration_manager_status(self) -> Dict[str, Any]:
        """
        Lấy trạng thái của Integration Manager.

        Returns:
            Dict[str, Any]: Thông tin trạng thái Integration Manager
        """
        return {
            "available": integration_manager_available,
            "enabled": self.use_integration_manager,
            "initialized": self.integration_manager is not None,
            "configuration": {
                "auto_integrate": self.integration_auto_integrate,
                "required_modules": self.integration_required_modules,
                "optional_modules": self.integration_optional_modules,
                "fallback_enabled": self.integration_fallback_enabled,
                "verbose": self.integration_verbose,
                "config": self.integration_config
            } if self.use_integration_manager else None,
            "manager_active": self.integration_manager is not None,
            "all_modules_integrated": self.all_modules_integrated,
            "methods_available": [
                "detect_integrated_modules",
                "integrate_single_module",
                "integrate_all_modules_manual",
                "get_module_integration_status",
                "get_integration_summary",
                "validate_integration",
                "repair_failed_integrations"
            ] if self.integration_manager else []
        }

    def get_integration_summary(self) -> Dict[str, Any]:
        """
        Lấy tóm tắt tích hợp của tất cả các module.

        Returns:
            Dict[str, Any]: Tóm tắt tích hợp
        """
        if not self.use_integration_manager or not self.integration_manager:
            return {
                "total_modules": 0,
                "integrated_count": 0,
                "failed_count": 0,
                "success_rate": 0.0,
                "status": "Integration Manager not available"
            }

        try:
            integrated_modules = self.integration_manager.get_integrated_modules()
            failed_modules = self.integration_manager.get_failed_modules()
            total_modules = len(integrated_modules) + len(failed_modules)

            success_rate = (len(integrated_modules) / total_modules * 100) if total_modules > 0 else 0.0

            return {
                "total_modules": total_modules,
                "integrated_count": len(integrated_modules),
                "failed_count": len(failed_modules),
                "success_rate": round(success_rate, 2),
                "integrated_modules": list(integrated_modules),
                "failed_modules": list(failed_modules),
                "status": "All modules integrated" if self.all_modules_integrated else "Partial integration",
                "auto_integration": self.integration_auto_integrate,
                "fallback_enabled": self.integration_fallback_enabled
            }
        except Exception as e:
            logger.error(f"Failed to get integration summary: {str(e)}")
            return {
                "total_modules": 0,
                "integrated_count": 0,
                "failed_count": 0,
                "success_rate": 0.0,
                "status": f"Error: {str(e)}"
            }

    def validate_integration(self) -> Dict[str, Any]:
        """
        Xác thực tích hợp của các module.

        Returns:
            Dict[str, Any]: Kết quả xác thực
        """
        validation_results = {
            "overall_status": "unknown",
            "modules_validated": {},
            "critical_issues": [],
            "warnings": [],
            "recommendations": []
        }

        try:
            # Validate each integrated module
            modules_to_validate = [
                ("resource_manager", self.resource_manager),
                ("error_utils", self.use_error_handling),
                ("playwright_handler", self.playwright_handler),
                ("file_processor", self.use_file_processor),
                ("content_extractor", self.use_content_extraction),
                ("performance_metrics", self.performance_metrics),
                ("user_agent_manager", self.user_agent_manager),
                ("site_structure_handler", self.site_structure_handler),
                ("language_handler", self.language_handler),
                ("integration_manager", self.integration_manager)
            ]

            validated_count = 0
            total_count = len(modules_to_validate)

            for module_name, module_instance in modules_to_validate:
                if module_instance:
                    validation_results["modules_validated"][module_name] = "active"
                    validated_count += 1
                else:
                    validation_results["modules_validated"][module_name] = "inactive"

            # Determine overall status
            if validated_count == total_count:
                validation_results["overall_status"] = "excellent"
            elif validated_count >= total_count * 0.8:
                validation_results["overall_status"] = "good"
            elif validated_count >= total_count * 0.5:
                validation_results["overall_status"] = "fair"
            else:
                validation_results["overall_status"] = "poor"

            # Add recommendations
            if validated_count < total_count:
                missing_modules = [name for name, instance in modules_to_validate if not instance]
                validation_results["recommendations"].append(
                    f"Consider enabling these modules: {', '.join(missing_modules)}"
                )

            if not self.all_modules_integrated:
                validation_results["warnings"].append("Not all modules have been integrated")

            validation_results["integration_score"] = round((validated_count / total_count) * 100, 2)

        except Exception as e:
            validation_results["critical_issues"].append(f"Validation failed: {str(e)}")
            validation_results["overall_status"] = "error"

        return validation_results

    def repair_failed_integrations(self) -> Dict[str, bool]:
        """
        Sửa chữa các tích hợp thất bại.

        Returns:
            Dict[str, bool]: Kết quả sửa chữa
        """
        if not self.use_integration_manager or not self.integration_manager:
            return {}

        try:
            failed_modules = self.integration_manager.get_failed_modules()
            repair_results = {}

            for module_name in failed_modules:
                try:
                    # Reset module status
                    if module_name in self.integration_manager.failed_modules:
                        self.integration_manager.failed_modules.remove(module_name)

                    # Retry integration
                    success = self.integration_manager.integrate_module(module_name)
                    repair_results[module_name] = success

                    if success:
                        logger.info(f"Successfully repaired integration for {module_name}")
                    else:
                        logger.warning(f"Failed to repair integration for {module_name}")

                except Exception as e:
                    logger.error(f"Error repairing {module_name}: {str(e)}")
                    repair_results[module_name] = False

            return repair_results
        except Exception as e:
            logger.error(f"Failed to repair failed integrations: {str(e)}")
            return {}