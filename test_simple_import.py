#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple import test for Site Structure Handler
"""

import sys
import os

# Add paths
sys.path.append('.')
sys.path.append('src')

def main():
    print("Testing simple import...", flush=True)
    
    try:
        print("Step 1: Importing crawler...", flush=True)
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged
        print("✅ Import successful", flush=True)
        
        print("Step 2: Creating crawler...", flush=True)
        crawler = AdaptiveCrawlerConsolidatedMerged()
        print("✅ Crawler created", flush=True)
        
        print("Step 3: Testing Site Structure Handler status...", flush=True)
        status = crawler.get_site_structure_status()
        print(f"Site Structure Handler status: {status}", flush=True)
        
        print("✅ All tests passed!", flush=True)
        
    except Exception as e:
        print(f"❌ Test failed: {e}", flush=True)
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
